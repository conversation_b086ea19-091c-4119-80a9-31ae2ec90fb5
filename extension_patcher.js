/**
 * Direct Extension Patcher for Augment VS Code Extension
 * 
 * This script directly modifies the extension.js file to intercept and replace
 * telemetry data with fake information. It targets the specific patterns that
 * collect the 41 data points mentioned by the user.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration
const EXTENSION_PATH = path.join(process.env.HOME, '.vscode/extensions/augment.vscode-augment-0.525.0');
const EXTENSION_JS_PATH = path.join(EXTENSION_PATH, 'out/extension.js');

// Generate consistent fake data targeting all 44+ data points
function generateFakeFingerprint() {
    const seed = 'augment-fake-data-seed';
    const hash = crypto.createHash('sha256').update(seed).digest('hex');
    const timestamp = Date.now();

    return {
        // CATEGORY A: IDENTIFICATION & AUTHENTICATION (8 Data)
        userId: `fake-user-${hash.substring(0, 8)}-${hash.substring(8, 12)}-${hash.substring(12, 16)}-${hash.substring(16, 20)}-${hash.substring(20, 32)}`,
        anonymousId: `anon-${hash.substring(32, 40)}-${hash.substring(40, 44)}-${hash.substring(44, 48)}-${hash.substring(48, 52)}-${hash.substring(52, 64)}`,
        sessionId: `session-${timestamp}-${hash.substring(0, 16)}`,
        messageId: `msg-${hash.substring(16, 32)}`,
        deviceId: `device-${hash.substring(32, 48)}`,
        machineId: `machine-${hash.substring(48, 64)}`,
        workspaceHash: `ws-${hash.substring(0, 32)}`,
        gitRepoId: `repo-${hash.substring(32, 64)}`,

        // CATEGORY B: SYSTEM & ENVIRONMENT (12 Data)
        platform: 'linux',
        arch: 'x64',
        osVersion: 'Ubuntu 22.04.3 LTS',
        vscodeVersion: '1.85.2',
        extensionVersion: '0.525.0',
        nodeVersion: '18.17.0',
        userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.85.2 Chrome/114.0.5735.289 Electron/25.8.4 Safari/537.36',
        locale: 'en-US',
        timezone: 'America/New_York',
        screenResolution: '1920x1080',
        totalMemory: 16 * 1024 * 1024 * 1024,
        cpuModel: 'Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz',
        homedir: '/home/<USER>',

        // CATEGORY C: WORKSPACE & PROJECT (8 Data)
        workspaceFolders: ['/home/<USER>/projects/fake-project'],
        activeFilePath: 'src/main.js',
        fileLanguage: 'javascript',
        gitBranch: 'main',
        gitCommitHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
        gitRemoteUrl: 'https://github.com/fakeuser/fake-project.git',
        fileCount: 42,
        projectType: 'node',

        // CATEGORY D: USER BEHAVIOR & USAGE (8 Data)
        commandExecutions: ['workbench.action.files.save', 'editor.action.formatDocument'],
        keystrokePatterns: { avgTypingSpeed: 65, avgPauseTime: 150 },
        mouseEvents: { clicksPerMinute: 12, avgClickDuration: 120 },
        sessionDuration: 3600000, // 1 hour
        featureUsage: { autocomplete: 45, search: 23, debug: 8 },
        errorOccurrences: [],
        performanceMetrics: { avgResponseTime: 150, memoryUsage: 0.6 },
        activationTime: 1200,

        // CATEGORY E: NETWORK & CONNECTIVITY (5 Data)
        ipAddress: '*************',
        networkType: 'wifi',
        connectionSpeed: 100, // Mbps
        remoteAgentStatus: false,
        apiResponseTimes: { search: 200, autocomplete: 50, analysis: 500 },

        // CATEGORY F: ADDITIONAL METADATA (3+ Data)
        installationDate: new Date(timestamp - 30 * 24 * 60 * 60 * 1000).toISOString(),
        lastUpdateDate: new Date(timestamp - 7 * 24 * 60 * 60 * 1000).toISOString(),
        configurationSettings: { theme: 'dark', fontSize: 14, tabSize: 2 },

        // Additional system info
        hostname: 'fake-machine-linux',
        username: 'fakeuser',
        macAddress: 'aa:bb:cc:dd:ee:ff'
    };
}

// More comprehensive patterns to search and replace in the extension
function getReplacementPatterns() {
    const fakeData = generateFakeFingerprint();

    return [
        // OS module calls - comprehensive replacement
        {
            search: /require\(['"]os['"]\)/g,
            replace: `(function() {
                const fakeOs = {
                    platform: () => '${fakeData.platform}',
                    arch: () => '${fakeData.arch}',
                    release: () => '${fakeData.version}',
                    hostname: () => '${fakeData.hostname}',
                    homedir: () => '${fakeData.homedir}',
                    userInfo: () => ({ username: '${fakeData.username}', homedir: '${fakeData.homedir}' }),
                    totalmem: () => ${fakeData.totalMemory},
                    freemem: () => ${Math.floor(fakeData.totalMemory / 2)},
                    uptime: () => 86400,
                    loadavg: () => [0.5, 0.7, 0.8],
                    networkInterfaces: () => ({
                        eth0: [{
                            address: '*************',
                            netmask: '*************',
                            family: 'IPv4',
                            mac: '${fakeData.macAddress}',
                            internal: false
                        }]
                    }),
                    cpus: () => Array(8).fill({
                        model: '${fakeData.cpuModel}',
                        speed: 3700,
                        times: { user: 1000, nice: 0, sys: 500, idle: 10000, irq: 0 }
                    }),
                    EOL: '\\n',
                    type: () => 'Linux',
                    tmpdir: () => '/tmp',
                    endianness: () => 'LE'
                };
                return fakeOs;
            })()`
        },

        // Process object comprehensive replacement
        {
            search: /process\.platform/g,
            replace: `'${fakeData.platform}'`
        },

        {
            search: /process\.arch/g,
            replace: `'${fakeData.arch}'`
        },

        {
            search: /process\.versions/g,
            replace: `{ node: '${fakeData.nodeVersion}', v8: '***********', uv: '1.44.2', zlib: '1.2.11', brotli: '1.0.9', ares: '1.18.1', modules: '108', nghttp2: '1.47.0', napi: '8', llhttp: '6.0.4', openssl: '3.0.2+quic', cldr: '41.0', icu: '71.1', tz: '2022a', unicode: '14.0' }`
        },

        {
            search: /process\.pid/g,
            replace: `${Math.floor(Math.random() * 65535)}`
        },

        {
            search: /process\.ppid/g,
            replace: `${Math.floor(Math.random() * 65535)}`
        },

        // System identification patterns
        {
            search: /getMachineId\(\)|machine.*id|machineId/gi,
            replace: `'${fakeData.machineId}'`
        },

        {
            search: /getDeviceId\(\)|device.*id|deviceId/gi,
            replace: `'${fakeData.deviceId}'`
        },

        {
            search: /getHardwareFingerprint\(\)|hardware.*id|hardwareId/gi,
            replace: `'${fakeData.hardwareId}'`
        },

        // Network and system info
        {
            search: /os\.networkInterfaces\(\)/g,
            replace: `({ eth0: [{ mac: '${fakeData.macAddress}', address: '*************', family: 'IPv4', internal: false }] })`
        },

        {
            search: /os\.hostname\(\)/g,
            replace: `'${fakeData.hostname}'`
        },

        {
            search: /os\.userInfo\(\)/g,
            replace: `{ username: '${fakeData.username}', homedir: '${fakeData.homedir}', uid: 1000, gid: 1000, shell: '/bin/bash' }`
        },

        {
            search: /os\.totalmem\(\)/g,
            replace: `${fakeData.totalMemory}`
        },

        {
            search: /os\.freemem\(\)/g,
            replace: `${Math.floor(fakeData.totalMemory / 2)}`
        },

        // Environment variables - comprehensive
        {
            search: /process\.env\.([A-Z_][A-Z0-9_]*)/g,
            replace: (match, envVar) => {
                const fakeEnvVars = {
                    'USER': fakeData.username,
                    'USERNAME': fakeData.username,
                    'HOME': fakeData.homedir,
                    'HOMEPATH': fakeData.homedir,
                    'HOSTNAME': fakeData.hostname,
                    'COMPUTERNAME': fakeData.hostname,
                    'SHELL': '/bin/bash',
                    'TERM': 'xterm-256color',
                    'PWD': '/home/<USER>',
                    'LANG': 'en_US.UTF-8',
                    'PATH': '/usr/local/bin:/usr/bin:/bin',
                    'NODE_ENV': 'production'
                };
                return fakeEnvVars[envVar] ? `'${fakeEnvVars[envVar]}'` : match;
            }
        },

        // Additional system calls that might be used for fingerprinting
        {
            search: /require\(['"]crypto['"]\)\.randomUUID\(\)/g,
            replace: `'${fakeData.machineId}'`
        },

        {
            search: /crypto\.randomUUID\(\)/g,
            replace: `'${fakeData.machineId}'`
        },

        // VS Code specific patterns
        {
            search: /vscode\.env\.machineId/g,
            replace: `'${fakeData.machineId}'`
        },

        {
            search: /vscode\.env\.sessionId/g,
            replace: `'${crypto.randomUUID()}'`
        }
    ];
}

function patchExtensionFile() {
    try {
        console.log('🔍 Reading extension file...');
        let content = fs.readFileSync(EXTENSION_JS_PATH, 'utf8');
        
        // Check if already patched
        if (content.includes('fake-machine-linux')) {
            console.log('⚠️  Extension appears to already be patched');
            return false;
        }
        
        console.log('🔧 Applying patches...');
        const patterns = getReplacementPatterns();
        let patchCount = 0;
        
        patterns.forEach((pattern, index) => {
            const beforeLength = content.length;
            if (typeof pattern.replace === 'function') {
                content = content.replace(pattern.search, pattern.replace);
            } else {
                content = content.replace(pattern.search, pattern.replace);
            }
            const afterLength = content.length;
            
            if (beforeLength !== afterLength) {
                patchCount++;
                console.log(`✅ Applied patch ${index + 1}: ${pattern.search.toString().substring(0, 50)}...`);
            }
        });
        
        if (patchCount > 0) {
            // Create backup
            const backupPath = EXTENSION_JS_PATH + '.backup';
            if (!fs.existsSync(backupPath)) {
                fs.copyFileSync(EXTENSION_JS_PATH, backupPath);
                console.log('💾 Created backup at:', backupPath);
            }
            
            // Write patched file
            fs.writeFileSync(EXTENSION_JS_PATH, content);
            console.log(`✅ Successfully applied ${patchCount} patches to extension`);
            console.log('🔄 Please restart VS Code for changes to take effect');
            return true;
        } else {
            console.log('ℹ️  No patches were applied (patterns not found)');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Error patching extension:', error.message);
        return false;
    }
}

function restoreExtension() {
    const backupPath = EXTENSION_JS_PATH + '.backup';
    
    if (!fs.existsSync(backupPath)) {
        console.log('❌ No backup file found');
        return false;
    }
    
    try {
        fs.copyFileSync(backupPath, EXTENSION_JS_PATH);
        console.log('✅ Extension restored from backup');
        console.log('🔄 Please restart VS Code for changes to take effect');
        return true;
    } catch (error) {
        console.error('❌ Error restoring extension:', error.message);
        return false;
    }
}

function checkExtension() {
    if (!fs.existsSync(EXTENSION_JS_PATH)) {
        console.error('❌ Augment extension not found at:', EXTENSION_JS_PATH);
        console.log('Please ensure the extension is installed and the path is correct');
        return false;
    }
    
    const stats = fs.statSync(EXTENSION_JS_PATH);
    console.log('📁 Extension file found');
    console.log('📊 File size:', Math.round(stats.size / 1024), 'KB');
    console.log('📅 Modified:', stats.mtime.toISOString());
    
    return true;
}

function showFakeData() {
    const fakeData = generateFakeFingerprint();
    console.log('🎭 Fake data that will be used:');
    console.log(JSON.stringify(fakeData, null, 2));
}

function main() {
    const command = process.argv[2];
    
    console.log('🚀 Augment Extension Telemetry Patcher');
    console.log('=====================================');
    
    if (!checkExtension()) {
        return;
    }
    
    switch (command) {
        case 'patch':
            patchExtensionFile();
            break;
        case 'restore':
            restoreExtension();
            break;
        case 'check':
            console.log('✅ Extension file is accessible');
            break;
        case 'show-fake-data':
            showFakeData();
            break;
        default:
            console.log('Usage: node extension_patcher.js [command]');
            console.log('');
            console.log('Commands:');
            console.log('  patch          - Apply fake telemetry patches');
            console.log('  restore        - Restore original extension from backup');
            console.log('  check          - Check if extension file exists');
            console.log('  show-fake-data - Show the fake data that will be used');
            console.log('');
            console.log('⚠️  Warning: This modifies the Augment extension files.');
            console.log('   Always backup your data before proceeding.');
    }
}

if (require.main === module) {
    main();
}

module.exports = {
    patchExtensionFile,
    restoreExtension,
    checkExtension,
    generateFakeFingerprint
};
