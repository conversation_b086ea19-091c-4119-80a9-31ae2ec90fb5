/**
 * Network Monitor for Augment Extension
 * 
 * This script helps you monitor network requests made by the Augment extension
 * to identify telemetry endpoints and data being sent.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const LOG_FILE = path.join(__dirname, 'augment_network_log.json');
const AUGMENT_DOMAINS = [
    'augmentcode.com',
    'api.augmentcode.com',
    'telemetry.augmentcode.com',
    'analytics.augmentcode.com',
    'metrics.augmentcode.com',
    'tracking.augmentcode.com'
];

class NetworkMonitor {
    constructor() {
        this.requests = [];
        this.startTime = Date.now();
        this.setupInterception();
    }

    setupInterception() {
        // Intercept HTTP/HTTPS requests
        this.interceptHttpRequests();
        
        // Intercept fetch API
        this.interceptFetchAPI();
        
        // Intercept XMLHttpRequest
        this.interceptXHR();
        
        console.log('🔍 Network monitoring started for Augment extension');
        console.log('📝 Logs will be saved to:', LOG_FILE);
    }

    interceptHttpRequests() {
        const http = require('http');
        const https = require('https');
        
        const originalHttpRequest = http.request;
        const originalHttpsRequest = https.request;
        
        http.request = (...args) => {
            this.logRequest('HTTP', args);
            return originalHttpRequest.apply(http, args);
        };
        
        https.request = (...args) => {
            this.logRequest('HTTPS', args);
            return originalHttpsRequest.apply(https, args);
        };
    }

    interceptFetchAPI() {
        if (typeof global !== 'undefined' && global.fetch) {
            const originalFetch = global.fetch;
            
            global.fetch = async (url, options = {}) => {
                this.logFetchRequest(url, options);
                return originalFetch(url, options);
            };
        }
    }

    interceptXHR() {
        if (typeof global !== 'undefined' && global.XMLHttpRequest) {
            const originalXHR = global.XMLHttpRequest;
            
            global.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                xhr.open = function(method, url, ...args) {
                    this._method = method;
                    this._url = url;
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                xhr.send = function(data) {
                    monitor.logXHRRequest(this._method, this._url, data);
                    return originalSend.apply(this, [data]);
                };
                
                return xhr;
            };
        }
    }

    logRequest(protocol, args) {
        try {
            const options = args[0];
            let url, hostname, path, method;
            
            if (typeof options === 'string') {
                url = options;
                const urlObj = new URL(url);
                hostname = urlObj.hostname;
                path = urlObj.pathname;
                method = 'GET';
            } else if (options && typeof options === 'object') {
                hostname = options.hostname || options.host;
                path = options.path || '/';
                method = options.method || 'GET';
                url = `${protocol.toLowerCase()}://${hostname}${path}`;
            }
            
            if (this.isAugmentRelated(hostname, url)) {
                const requestData = {
                    timestamp: Date.now(),
                    protocol,
                    method,
                    url,
                    hostname,
                    path,
                    headers: options.headers || {},
                    type: 'HTTP_REQUEST'
                };
                
                this.requests.push(requestData);
                this.saveLog();
                
                console.log(`🌐 ${protocol} ${method} ${url}`);
                if (options.headers) {
                    console.log('📋 Headers:', JSON.stringify(options.headers, null, 2));
                }
            }
        } catch (error) {
            console.error('Error logging request:', error);
        }
    }

    logFetchRequest(url, options) {
        try {
            if (this.isAugmentRelated(null, url)) {
                const requestData = {
                    timestamp: Date.now(),
                    method: options.method || 'GET',
                    url: url.toString(),
                    headers: options.headers || {},
                    body: options.body,
                    type: 'FETCH_REQUEST'
                };
                
                this.requests.push(requestData);
                this.saveLog();
                
                console.log(`🚀 FETCH ${requestData.method} ${url}`);
                if (options.body) {
                    console.log('📦 Body:', this.truncateData(options.body));
                }
            }
        } catch (error) {
            console.error('Error logging fetch request:', error);
        }
    }

    logXHRRequest(method, url, data) {
        try {
            if (this.isAugmentRelated(null, url)) {
                const requestData = {
                    timestamp: Date.now(),
                    method: method || 'GET',
                    url,
                    data: this.truncateData(data),
                    type: 'XHR_REQUEST'
                };
                
                this.requests.push(requestData);
                this.saveLog();
                
                console.log(`📡 XHR ${method} ${url}`);
                if (data) {
                    console.log('📦 Data:', this.truncateData(data));
                }
            }
        } catch (error) {
            console.error('Error logging XHR request:', error);
        }
    }

    isAugmentRelated(hostname, url) {
        const urlString = url ? url.toString().toLowerCase() : '';
        const hostnameString = hostname ? hostname.toLowerCase() : '';
        
        // Check for Augment-related domains
        for (const domain of AUGMENT_DOMAINS) {
            if (hostnameString.includes(domain) || urlString.includes(domain)) {
                return true;
            }
        }
        
        // Check for telemetry/analytics keywords in URL
        const telemetryKeywords = [
            'telemetry', 'analytics', 'metrics', 'tracking', 'stats',
            'usage', 'events', 'logs', 'report', 'collect', 'send'
        ];
        
        for (const keyword of telemetryKeywords) {
            if (urlString.includes(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    truncateData(data) {
        if (!data) return null;
        
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);
        return dataString.length > 1000 ? dataString.substring(0, 1000) + '...' : dataString;
    }

    saveLog() {
        try {
            const logData = {
                startTime: this.startTime,
                lastUpdate: Date.now(),
                totalRequests: this.requests.length,
                requests: this.requests
            };
            
            fs.writeFileSync(LOG_FILE, JSON.stringify(logData, null, 2));
        } catch (error) {
            console.error('Error saving log:', error);
        }
    }

    generateReport() {
        console.log('\n📊 Network Monitoring Report');
        console.log('=' .repeat(50));
        console.log(`Total requests captured: ${this.requests.length}`);
        console.log(`Monitoring duration: ${Math.round((Date.now() - this.startTime) / 1000)}s`);
        
        if (this.requests.length > 0) {
            console.log('\n🎯 Captured Requests:');
            this.requests.forEach((req, index) => {
                console.log(`\n${index + 1}. ${req.type} - ${req.method} ${req.url}`);
                console.log(`   Time: ${new Date(req.timestamp).toISOString()}`);
                if (req.headers && Object.keys(req.headers).length > 0) {
                    console.log(`   Headers: ${JSON.stringify(req.headers)}`);
                }
                if (req.body || req.data) {
                    console.log(`   Data: ${this.truncateData(req.body || req.data)}`);
                }
            });
        }
        
        console.log(`\n💾 Full log saved to: ${LOG_FILE}`);
    }

    stop() {
        this.generateReport();
        console.log('\n🛑 Network monitoring stopped');
    }
}

// Create global monitor instance
const monitor = new NetworkMonitor();

// Handle process termination
process.on('SIGINT', () => {
    monitor.stop();
    process.exit(0);
});

process.on('SIGTERM', () => {
    monitor.stop();
    process.exit(0);
});

// Export for use in other scripts
module.exports = NetworkMonitor;

console.log('🚀 Network monitor is running...');
console.log('Press Ctrl+C to stop and generate report');
