import{A as J,C as ne,m as b,X as L,Y as f,K as D,_ as e,$ as ce,D as K,J as M,t as g,M as F,T as N,V as S,b as n,F as de,G as pe,H as P,L as ue,I as X,ak as ve,P as me,al as fe,a2 as ge,w as he,a4 as $e,az as ye}from"./SpinnerAugment-BY2Lraps.js";import"./design-system-init-DkEuonq_.js";import{c as w,W as A}from"./IconButtonAugment-B8y0FMb_.js";import{B as be}from"./ButtonAugment-BoJU5mQc.js";import{O as we}from"./OpenFileButton-DfoRAJ9f.js";import{C as Ee,R as Re,E as ze,T as Fe,a as G}from"./index-Ci1AwCq1.js";import{M as Y,R as _}from"./message-broker-BauNv3yh.js";import{M as Me}from"./MarkdownEditor-DyMkuzW7.js";import{R as ke}from"./RulesModeSelector-e941xJbL.js";import{C as Ce}from"./chevron-left-NBNCkIlC.js";import{T as Te}from"./CardAugment-BaFOe6RO.js";import{l as xe}from"./chevron-down-CVLGkBkY.js";import"./chat-model-context-C9JFkoqk.js";import"./index-C4gKbsWy.js";import"./index-CoHT-xzg.js";import"./remote-agents-client-XI3B217g.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-3WeVBTrk.js";import"./TextAreaAugment-BkN7aH6v.js";import"./BaseTextInput-C64uUToe.js";import"./async-messaging-BnOo7nYC.js";import"./focusTrapStack-CAuiPHBF.js";import"./isObjectLike-DuRpH5zX.js";var Le=M('<div class="c-rule-config svelte-1r8al3d"><div class="c-rule-field c-rule-field-full-width svelte-1r8al3d"><!> <!></div></div>'),De=M('<div class="l-file-controls svelte-1r8al3d" slot="header"><div class="l-file-controls-left svelte-1r8al3d"><div class="c-trigger-section svelte-1r8al3d"><!> <!> <!></div></div> <!></div>'),Ne=M("<div>Loading...</div>"),Se=M('<div class="c-rules-container svelte-1vbu0zh"><!></div>');ye(function(q,Q){J(Q,!1);const[U,V]=ge(),O=()=>$e(B,"$rule",U),W=new Y(w),B=he(null),j={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===A.loadFile&&t){const h=t.data.content;if(h!==void 0){const a=h.replace(/^\n+/,""),o=G.getDescriptionFrontmatterKey(a),E=G.getRuleTypeFromContent(a),r=G.extractContent(a);B.set({path:t.data.pathName,content:r,type:E,description:o})}}return!0}};ve(()=>{W.registerConsumer(j),w.postMessage({type:A.rulesLoaded})}),K();var H=Se();me("message",fe,function(...s){var t;(t=W.onMessageFromExtension)==null||t.apply(this,s)});var Z=g(H),ee=s=>{(function(t,h){J(h,!1);const a=b(),o=b(),E=b();let r=ne(h,"rule",12),$=b(r().content),i=b(r().description);const I=new Y(w),se=new Ee,ae=new ze(w,I,se),re=new Re(I),k=async(l,p)=>{r({...r(),type:l,description:p||e(i)}),p!==void 0&&f(i,p);try{await re.updateRuleContent({type:l,path:e(a),content:e($),description:p||e(i)})}catch(c){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",c)}},oe=xe.debounce(k,500),ie=()=>{w.postMessage({type:A.openSettingsPage,data:"guidelines"})};L(()=>D(r()),()=>{f(a,r().path)}),L(()=>D(r()),()=>{f(o,r().type)}),L(()=>(e(a),e(o),e($),e(i)),()=>{f(E,{path:e(a),type:e(o),content:e($),description:e(i)})}),ce(),K(),Me(t,{saveFunction:()=>k(e(o),e(i)),variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get value(){return e($)},set value(l){f($,l)},children:(l,p)=>{var c=de(),R=pe(c),C=u=>{var y=Le(),T=g(y),z=g(T);N(z,{size:1,class:"c-field-label",children:(v,m)=>{var x=S("Description");n(v,x)},$$slots:{default:!0}});var d=F(z,2);Fe(d,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(i)},set value(v){f(i,v)},$$events:{input:()=>oe(e(o),e(i))},$$legacy:!0}),n(u,y)};P(R,u=>{e(o),D(_),ue(()=>e(o)===_.AGENT_REQUESTED)&&u(C)}),n(l,c)},$$slots:{default:!0,header:(l,p)=>{var c=De(),R=g(c),C=g(R),u=g(C);Te(u,{content:"Navigate back to all Rules & Guidelines",children:(d,v)=>{be(d,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(m,x)=>{Ce(m,{slot:"iconLeft"})}}})},$$slots:{default:!0}});var y=F(u,2);N(y,{size:1,class:"c-field-label",children:(d,v)=>{var m=S("Trigger:");n(d,m)},$$slots:{default:!0}});var T=F(y,2);ke(T,{onSave:k,get rule(){return e(E)}});var z=F(R,2);we(z,{size:1,get path(){return e(a)},onOpenLocalFile:async()=>(ae.openFile({repoRoot:"",pathName:e(a)}),"success"),$$slots:{text:(d,v)=>{N(d,{slot:"text",size:1,children:(m,x)=>{var le=S("Open file");n(m,le)},$$slots:{default:!0}})}}}),n(l,c)}},$$legacy:!0}),X()})(s,{get rule(){return O()}})},te=s=>{var t=Ne();n(s,t)};P(Z,s=>{O()!==null?s(ee):s(te,!1)}),n(q,H),X(),V()},{target:document.getElementById("app")});
