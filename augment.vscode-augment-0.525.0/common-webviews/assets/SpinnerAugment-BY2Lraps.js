var Cc=Object.defineProperty;var Rc=(e,t,n)=>t in e?Cc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var hr=(e,t,n)=>Rc(e,typeof t!="symbol"?t+"":t,n);(function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const n of document.querySelectorAll('link[rel="modulepreload"]'))t(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const s of r.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&t(s)}).observe(document,{childList:!0,subtree:!0})}function t(n){if(n.ep)return;n.ep=!0;const r=function(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}(n);fetch(n.href,r)}})();const O=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,rt="9.20.0",q=globalThis;function gt(){return er(q),q}function er(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||rt,t[rt]=t[rt]||{}}function qn(e,t,n=q){const r=n.__SENTRY__=n.__SENTRY__||{},s=r[rt]=r[rt]||{};return s[e]||(s[e]=t())}const Ii=Object.prototype.toString;function ms(e){switch(Ii.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return Ne(e,Error)}}function Dt(e,t){return Ii.call(e)===`[object ${t}]`}function Pi(e){return Dt(e,"ErrorEvent")}function Bs(e){return Dt(e,"DOMError")}function Re(e){return Dt(e,"String")}function hs(e){return typeof e=="object"&&e!==null&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function Xt(e){return e===null||hs(e)||typeof e!="object"&&typeof e!="function"}function Zt(e){return Dt(e,"Object")}function tr(e){return typeof Event<"u"&&Ne(e,Event)}function nr(e){return!!(e!=null&&e.then&&typeof e.then=="function")}function Ne(e,t){try{return e instanceof t}catch{return!1}}function Ai(e){return!(typeof e!="object"||e===null||!e.__isVue&&!e._isVue)}function Oi(e){return typeof Request<"u"&&Ne(e,Request)}const gs=q,Dc=80;function lt(e,t={}){if(!e)return"<unknown>";try{let n=e;const r=5,s=[];let o=0,i=0;const a=" > ",c=a.length;let u;const l=Array.isArray(t)?t:t.keyAttrs,p=!Array.isArray(t)&&t.maxStringLength||Dc;for(;n&&o++<r&&(u=Nc(n,l),!(u==="html"||o>1&&i+s.length*c+u.length>=p));)s.push(u),i+=u.length,n=n.parentNode;return s.reverse().join(a)}catch{return"<unknown>"}}function Nc(e,t){const n=e,r=[];if(!(n!=null&&n.tagName))return"";if(gs.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=t!=null&&t.length?t.filter(i=>n.getAttribute(i)).map(i=>[i,n.getAttribute(i)]):null;if(s!=null&&s.length)s.forEach(i=>{r.push(`[${i[0]}="${i[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const i=n.className;if(i&&Re(i)){const a=i.split(/\s+/);for(const c of a)r.push(`.${c}`)}}const o=["aria-label","type","name","title","alt"];for(const i of o){const a=n.getAttribute(i);a&&r.push(`[${i}="${a}"]`)}return r.join("")}function pn(){try{return gs.document.location.href}catch{return""}}function Ci(e){if(!gs.HTMLElement)return null;let t=e;for(let n=0;n<5;n++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}const qr=["debug","info","warn","error","log","assert","trace"],Fn={};function vt(e){if(!("console"in q))return e();const t=q.console,n={},r=Object.keys(Fn);r.forEach(s=>{const o=Fn[s];n[s]=t[s],t[s]=o});try{return e()}finally{r.forEach(s=>{t[s]=n[s]})}}const x=qn("logger",function(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return O?qr.forEach(n=>{t[n]=(...r)=>{e&&vt(()=>{q.console[n](`Sentry Logger [${n}]:`,...r)})}}):qr.forEach(n=>{t[n]=()=>{}}),t});function Un(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.slice(0,t)}...`}function zs(e,t){if(!Array.isArray(e))return"";const n=[];for(let r=0;r<e.length;r++){const s=e[r];try{Ai(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(t)}function Lc(e,t,n=!1){return!!Re(e)&&(Dt(t,"RegExp")?t.test(e):!!Re(t)&&(n?e===t:e.includes(t)))}function nt(e,t=[],n=!1){return t.some(r=>Lc(e,r,n))}function ue(e,t,n){if(!(t in e))return;const r=e[t];if(typeof r!="function")return;const s=n(r);typeof s=="function"&&Ri(s,r);try{e[t]=s}catch{O&&x.log(`Failed to replace method "${t}" in object`,e)}}function de(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch{O&&x.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function Ri(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,de(e,"__sentry_original__",t)}catch{}}function vs(e){return e.__sentry_original__}function Di(e){if(ms(e))return{message:e.message,name:e.name,stack:e.stack,...Js(e)};if(tr(e)){const t={type:e.type,target:Ws(e.target),currentTarget:Ws(e.currentTarget),...Js(e)};return typeof CustomEvent<"u"&&Ne(e,CustomEvent)&&(t.detail=e.detail),t}return e}function Ws(e){try{return t=e,typeof Element<"u"&&Ne(t,Element)?lt(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}var t}function Js(e){if(typeof e=="object"&&e!==null){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}return{}}function ge(e=function(){const t=q;return t.crypto||t.msCrypto}()){let t=()=>16*Math.random();try{if(e!=null&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e!=null&&e.getRandomValues&&(t=()=>{const n=new Uint8Array(1);return e.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(15&t())>>n/4).toString(16))}function Ni(e){var t,n;return(n=(t=e.exception)==null?void 0:t.values)==null?void 0:n[0]}function et(e){const{message:t,event_id:n}=e;if(t)return t;const r=Ni(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Fr(e,t,n){const r=e.exception=e.exception||{},s=r.values=r.values||[],o=s[0]=s[0]||{};o.value||(o.value=t||""),o.type||(o.type="Error")}function Tt(e,t){const n=Ni(e);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...t},t&&"data"in t){const s={...r==null?void 0:r.data,...t.data};n.mechanism.data=s}}function Gs(e){if(function(t){try{return t.__sentry_captured__}catch{}}(e))return!0;try{de(e,"__sentry_captured__",!0)}catch{}return!1}const Li=1e3;function mn(){return Date.now()/Li}const ne=function(){const{performance:e}=q;if(!(e!=null&&e.now))return mn;const t=Date.now()-e.now(),n=e.timeOrigin==null?t:e.timeOrigin;return()=>(n+e.now())/Li}();let gr;function he(){return gr||(gr=function(){var c;const{performance:e}=q;if(!(e!=null&&e.now))return[void 0,"none"];const t=36e5,n=e.now(),r=Date.now(),s=e.timeOrigin?Math.abs(e.timeOrigin+n-r):t,o=s<t,i=(c=e.timing)==null?void 0:c.navigationStart,a=typeof i=="number"?Math.abs(i+n-r):t;return o||a<t?s<=a?[e.timeOrigin,"timeOrigin"]:[i,"navigationStart"]:[r,"dateNow"]}()),gr[0]}function Mc(e){const t=ne(),n={sid:ge(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(r){return{sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:typeof r.did=="number"||typeof r.did=="string"?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}}}(n)};return e&&kt(n,e),n}function kt(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||ne(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:ge()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{const n=e.timestamp-e.started;e.duration=n>=0?n:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function hn(e,t,n=2){if(!t||typeof t!="object"||n<=0)return t;if(e&&Object.keys(t).length===0)return e;const r={...e};for(const s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=hn(r[s],t[s],n-1));return r}const Ur="_sentrySpan";function Qt(e,t){t?de(e,Ur,t):delete e[Ur]}function Hn(e){return e[Ur]}function Be(){return ge()}function gn(){return ge().substring(16)}class Le{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:Be(),sampleRand:Math.random()}}clone(){const t=new Le;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},this._contexts.flags&&(t._contexts.flags={values:[...this._contexts.flags.values]}),t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,Qt(t,Hn(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&kt(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,n){return this._tags={...this._tags,[t]:n},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,n){return this._extra={...this._extra,[t]:n},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,n){return n===null?delete this._contexts[t]:this._contexts[t]=n,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const n=typeof t=="function"?t(this):t,r=n instanceof Le?n.getScopeData():Zt(n)?t:void 0,{tags:s,extra:o,user:i,contexts:a,level:c,fingerprint:u=[],propagationContext:l}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...o},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),c&&(this._level=c),u.length&&(this._fingerprint=u),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,Qt(this,void 0),this._attachments=[],this.setPropagationContext({traceId:Be(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(t,n){var o;const r=typeof n=="number"?n:100;if(r<=0)return this;const s={timestamp:mn(),...t,message:t.message?Un(t.message,2048):t.message};return this._breadcrumbs.push(s),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(o=this._client)==null||o.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:Hn(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata=hn(this._sdkProcessingMetadata,t,2),this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,n){const r=(n==null?void 0:n.event_id)||ge();if(!this._client)return x.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:s,...n,event_id:r},this),r}captureMessage(t,n,r){const s=(r==null?void 0:r.event_id)||ge();if(!this._client)return x.warn("No client configured on scope - will not capture message!"),s;const o=new Error(t);return this._client.captureMessage(t,n,{originalException:t,syntheticException:o,...r,event_id:s},this),s}captureEvent(t,n){const r=(n==null?void 0:n.event_id)||ge();return this._client?(this._client.captureEvent(t,{...n,event_id:r},this),r):(x.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}class jc{constructor(t,n){let r,s;r=t||new Le,s=n||new Le,this._stack=[{scope:r}],this._isolationScope=s}withScope(t){const n=this._pushScope();let r;try{r=t(n)}catch(s){throw this._popScope(),s}return nr(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function $t(){const e=er(gt());return e.stack=e.stack||new jc(qn("defaultCurrentScope",()=>new Le),qn("defaultIsolationScope",()=>new Le))}function qc(e){return $t().withScope(e)}function Fc(e,t){const n=$t();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function Ks(e){return $t().withScope(()=>e($t().getIsolationScope()))}function Nt(e){const t=er(e);return t.acs?t.acs:{withIsolationScope:Ks,withScope:qc,withSetScope:Fc,withSetIsolationScope:(n,r)=>Ks(r),getCurrentScope:()=>$t().getScope(),getIsolationScope:()=>$t().getIsolationScope()}}function W(){return Nt(gt()).getCurrentScope()}function Ye(){return Nt(gt()).getIsolationScope()}function _s(...e){const t=Nt(gt());if(e.length===2){const[n,r]=e;return n?t.withSetScope(n,r):t.withScope(r)}return t.withScope(e[0])}function H(){return W().getClient()}function Uc(e){const t=e.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:s}=t,o={trace_id:n,span_id:s||gn()};return r&&(o.parent_span_id=r),o}const Oe="sentry.source",ys="sentry.sample_rate",Mi="sentry.previous_trace_sample_rate",dt="sentry.op",X="sentry.origin",Bn="sentry.idle_span_finish_reason",rr="sentry.measurement_unit",sr="sentry.measurement_value",Vs="sentry.custom_span_name",Hr="sentry.profile_id",en="sentry.exclusive_time",Hc="sentry.link.type",Bc=0,ji=1,Y=2;function qi(e,t){e.setAttribute("http.response.status_code",t);const n=function(r){if(r<400&&r>=100)return{code:ji};if(r>=400&&r<500)switch(r){case 401:return{code:Y,message:"unauthenticated"};case 403:return{code:Y,message:"permission_denied"};case 404:return{code:Y,message:"not_found"};case 409:return{code:Y,message:"already_exists"};case 413:return{code:Y,message:"failed_precondition"};case 429:return{code:Y,message:"resource_exhausted"};case 499:return{code:Y,message:"cancelled"};default:return{code:Y,message:"invalid_argument"}}if(r>=500&&r<600)switch(r){case 501:return{code:Y,message:"unimplemented"};case 503:return{code:Y,message:"unavailable"};case 504:return{code:Y,message:"deadline_exceeded"};default:return{code:Y,message:"internal_error"}}return{code:Y,message:"unknown_error"}}(t);n.message!=="unknown_error"&&e.setStatus(n)}const Fi="_sentryScope",Ui="_sentryIsolationScope";function zn(e){return{scope:e[Fi],isolationScope:e[Ui]}}function tn(e){if(typeof e=="boolean")return Number(e);const t=typeof e=="string"?parseFloat(e):e;return typeof t!="number"||isNaN(t)||t<0||t>1?void 0:t}const bs="sentry-",zc=/^sentry-/,Wc=8192;function Hi(e){const t=function(r){if(!(!r||!Re(r)&&!Array.isArray(r)))return Array.isArray(r)?r.reduce((s,o)=>{const i=Ys(o);return Object.entries(i).forEach(([a,c])=>{s[a]=c}),s},{}):Ys(r)}(e);if(!t)return;const n=Object.entries(t).reduce((r,[s,o])=>(s.match(zc)&&(r[s.slice(bs.length)]=o),r),{});return Object.keys(n).length>0?n:void 0}function Jc(e){if(e)return function(t){if(Object.keys(t).length!==0)return Object.entries(t).reduce((n,[r,s],o)=>{const i=`${encodeURIComponent(r)}=${encodeURIComponent(s)}`,a=o===0?i:`${n},${i}`;return a.length>Wc?(O&&x.warn(`Not adding key: ${r} with val: ${s} to baggage header due to exceeding baggage size limits.`),n):a},"")}(Object.entries(e).reduce((t,[n,r])=>(r&&(t[`${bs}${n}`]=r),t),{}))}function Ys(e){return e.split(",").map(t=>t.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((t,[n,r])=>(n&&r&&(t[n]=r),t),{})}const Bi=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Gc(e,t){const n=function(c){if(!c)return;const u=c.match(Bi);if(!u)return;let l;return u[3]==="1"?l=!0:u[3]==="0"&&(l=!1),{traceId:u[1],parentSampled:l,parentSpanId:u[2]}}(e),r=Hi(t);if(!(n!=null&&n.traceId))return{traceId:Be(),sampleRand:Math.random()};const s=function(c,u){const l=tn(u==null?void 0:u.sample_rand);if(l!==void 0)return l;const p=tn(u==null?void 0:u.sample_rate);return p&&(c==null?void 0:c.parentSampled)!==void 0?c.parentSampled?Math.random()*p:p+Math.random()*(1-p):Math.random()}(n,r);r&&(r.sample_rand=s.toString());const{traceId:o,parentSpanId:i,parentSampled:a}=n;return{traceId:o,parentSpanId:i,sampled:a,dsc:r||{},sampleRand:s}}function Xs(e=Be(),t=gn(),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${e}-${t}${r}`}const Ss=1;let Zs=!1;function Kc(e){const{spanId:t,traceId:n}=e.spanContext(),{data:r,op:s,parent_span_id:o,status:i,origin:a,links:c}=B(e);return{parent_span_id:o,span_id:t,trace_id:n,data:r,op:s,status:i,origin:a,links:c}}function Vc(e){const{spanId:t,traceId:n,isRemote:r}=e.spanContext(),s=r?t:B(e).parent_span_id,o=zn(e).scope;return{parent_span_id:s,span_id:r?(o==null?void 0:o.getPropagationContext().propagationSpanId)||gn():t,trace_id:n}}function zi(e){return e&&e.length>0?e.map(({context:{spanId:t,traceId:n,traceFlags:r,...s},attributes:o})=>({span_id:t,trace_id:n,sampled:r===Ss,attributes:o,...s})):void 0}function st(e){return typeof e=="number"?Qs(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?Qs(e.getTime()):ne()}function Qs(e){return e>9999999999?e/1e3:e}function B(e){var r;if(function(s){return typeof s.getSpanJSON=="function"}(e))return e.getSpanJSON();const{spanId:t,traceId:n}=e.spanContext();if(function(s){const o=s;return!!(o.attributes&&o.startTime&&o.name&&o.endTime&&o.status)}(e)){const{attributes:s,startTime:o,name:i,endTime:a,status:c,links:u}=e;return{span_id:t,trace_id:n,data:s,description:i,parent_span_id:"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?(r=e.parentSpanContext)==null?void 0:r.spanId:void 0,start_timestamp:st(o),timestamp:st(a)||void 0,status:Wi(c),op:s[dt],origin:s[X],links:zi(u)}}return{span_id:t,trace_id:n,start_timestamp:0,data:{}}}function ot(e){const{traceFlags:t}=e.spanContext();return t===Ss}function Wi(e){if(e&&e.code!==Bc)return e.code===ji?"ok":e.message||"unknown_error"}const it="_sentryChildSpans",Br="_sentryRootSpan";function eo(e,t){const n=e[Br]||e;de(t,Br,n),e[it]?e[it].add(t):de(e,it,new Set([t]))}function Rn(e){const t=new Set;return function n(r){if(!t.has(r)&&ot(r)){t.add(r);const s=r[it]?Array.from(r[it]):[];for(const o of s)n(o)}}(e),Array.from(t)}function te(e){return e[Br]||e}function ce(){const e=Nt(gt());return e.getActiveSpan?e.getActiveSpan():Hn(W())}function zr(){Zs||(vt(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),Zs=!0)}const to=50,at="?",no=/\(error: (.*)\)/,ro=/captureMessage|captureException/;function Ji(...e){const t=e.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const o=[],i=n.split(`
`);for(let a=r;a<i.length;a++){const c=i[a];if(c.length>1024)continue;const u=no.test(c)?c.replace(no,"$1"):c;if(!u.match(/\S*Error: /)){for(const l of t){const p=l(u);if(p){o.push(p);break}}if(o.length>=to+s)break}}return function(a){if(!a.length)return[];const c=Array.from(a);return/sentryWrapped/.test(En(c).function||"")&&c.pop(),c.reverse(),ro.test(En(c).function||"")&&(c.pop(),ro.test(En(c).function||"")&&c.pop()),c.slice(0,to).map(u=>({...u,filename:u.filename||En(c).filename,function:u.function||at}))}(o.slice(s))}}function En(e){return e[e.length-1]||{}}const so="<anonymous>";function Me(e){try{return e&&typeof e=="function"&&e.name||so}catch{return so}}function oo(e){const t=e.exception;if(t){const n=[];try{return t.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const Dn={},io={};function ze(e,t){Dn[e]=Dn[e]||[],Dn[e].push(t)}function We(e,t){if(!io[e]){io[e]=!0;try{t()}catch(n){O&&x.error(`Error while instrumenting ${e}`,n)}}}function ve(e,t){const n=e&&Dn[e];if(n)for(const r of n)try{r(t)}catch(s){O&&x.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${Me(r)}
Error:`,s)}}let vr=null;function Gi(e){const t="error";ze(t,e),We(t,Yc)}function Yc(){vr=q.onerror,q.onerror=function(e,t,n,r,s){return ve("error",{column:r,error:s,line:n,msg:e,url:t}),!!vr&&vr.apply(this,arguments)},q.onerror.__SENTRY_INSTRUMENTED__=!0}let _r=null;function Ki(e){const t="unhandledrejection";ze(t,e),We(t,Xc)}function Xc(){_r=q.onunhandledrejection,q.onunhandledrejection=function(e){return ve("unhandledrejection",e),!_r||_r.apply(this,arguments)},q.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let ao=!1;function Wr(){const e=ce(),t=e&&te(e);if(t){const n="internal_error";O&&x.log(`[Tracing] Root span: ${n} -> Global error occurred`),t.setStatus({code:Y,message:n})}}function Je(e){var n;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const t=e||((n=H())==null?void 0:n.getOptions());return!(!t||t.tracesSampleRate==null&&!t.tracesSampler)}Wr.tag="sentry_tracingErrorCallback";const ws="production",Zc=/^o(\d+)\./,Qc=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function nn(e,t=!1){const{host:n,path:r,pass:s,port:o,projectId:i,protocol:a,publicKey:c}=e;return`${a}://${c}${t&&s?`:${s}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${i}`}function co(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function eu(e){const t=typeof e=="string"?function(n){const r=Qc.exec(n);if(!r)return void vt(()=>{console.error(`Invalid Sentry Dsn: ${n}`)});const[s,o,i="",a="",c="",u=""]=r.slice(1);let l="",p=u;const d=p.split("/");if(d.length>1&&(l=d.slice(0,-1).join("/"),p=d.pop()),p){const f=p.match(/^\d+/);f&&(p=f[0])}return co({host:a,pass:i,path:l,projectId:p,port:c,protocol:s,publicKey:o})}(e):co(e);if(t&&function(n){if(!O)return!0;const{port:r,projectId:s,protocol:o}=n;return!(["protocol","publicKey","host","projectId"].find(i=>!n[i]&&(x.error(`Invalid Sentry Dsn: ${i} missing`),!0))||(s.match(/^\d+$/)?function(i){return i==="http"||i==="https"}(o)?r&&isNaN(parseInt(r,10))&&(x.error(`Invalid Sentry Dsn: Invalid port ${r}`),1):(x.error(`Invalid Sentry Dsn: Invalid protocol ${o}`),1):(x.error(`Invalid Sentry Dsn: Invalid projectId ${s}`),1)))}(t))return t}const Vi="_frozenDsc";function Nn(e,t){de(e,Vi,t)}function Yi(e,t){const n=t.getOptions(),{publicKey:r,host:s}=t.getDsn()||{};let o;n.orgId?o=String(n.orgId):s&&(o=function(a){const c=a.match(Zc);return c==null?void 0:c[1]}(s));const i={environment:n.environment||ws,release:n.release,public_key:r,trace_id:e,org_id:o};return t.emit("createDsc",i),i}function Xi(e,t){const n=t.getPropagationContext();return n.dsc||Yi(n.traceId,e)}function Ge(e){var h;const t=H();if(!t)return{};const n=te(e),r=B(n),s=r.data,o=n.spanContext().traceState,i=(o==null?void 0:o.get("sentry.sample_rate"))??s[ys]??s[Mi];function a(m){return typeof i!="number"&&typeof i!="string"||(m.sample_rate=`${i}`),m}const c=n[Vi];if(c)return a(c);const u=o==null?void 0:o.get("sentry.dsc"),l=u&&Hi(u);if(l)return a(l);const p=Yi(e.spanContext().traceId,t),d=s[Oe],f=r.description;return d!=="url"&&f&&(p.transaction=f),Je()&&(p.sampled=String(ot(n)),p.sample_rand=(o==null?void 0:o.get("sentry.sample_rand"))??((h=zn(n).scope)==null?void 0:h.getPropagationContext().sampleRand.toString())),a(p),t.emit("createDsc",p,n),p}class ct{constructor(t={}){this._traceId=t.traceId||Be(),this._spanId=t.spanId||gn()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(t){}setAttribute(t,n){return this}setAttributes(t){return this}setStatus(t){return this}updateName(t){return this}isRecording(){return!1}addEvent(t,n,r){return this}addLink(t){return this}addLinks(t){return this}recordException(t,n){}}function Ie(e,t=100,n=1/0){try{return Jr("",e,t,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function Zi(e,t=3,n=102400){const r=Ie(e,t);return s=r,function(o){return~-encodeURI(o).split(/%..|./).length}(JSON.stringify(s))>n?Zi(e,t-1,n):r;var s}function Jr(e,t,n=1/0,r=1/0,s=function(){const o=new WeakSet;function i(c){return!!o.has(c)||(o.add(c),!1)}function a(c){o.delete(c)}return[i,a]}()){const[o,i]=s;if(t==null||["boolean","string"].includes(typeof t)||typeof t=="number"&&Number.isFinite(t))return t;const a=function(f,h){try{if(f==="domain"&&h&&typeof h=="object"&&h._events)return"[Domain]";if(f==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&h===global)return"[Global]";if(typeof window<"u"&&h===window)return"[Window]";if(typeof document<"u"&&h===document)return"[Document]";if(Ai(h))return"[VueViewModel]";if(Zt(m=h)&&"nativeEvent"in m&&"preventDefault"in m&&"stopPropagation"in m)return"[SyntheticEvent]";if(typeof h=="number"&&!Number.isFinite(h))return`[${h}]`;if(typeof h=="function")return`[Function: ${Me(h)}]`;if(typeof h=="symbol")return`[${String(h)}]`;if(typeof h=="bigint")return`[BigInt: ${String(h)}]`;const g=function(v){const _=Object.getPrototypeOf(v);return _!=null&&_.constructor?_.constructor.name:"null prototype"}(h);return/^HTML(\w*)Element$/.test(g)?`[HTMLElement: ${g}]`:`[object ${g}]`}catch(g){return`**non-serializable** (${g})`}var m}(e,t);if(!a.startsWith("[object "))return a;if(t.__sentry_skip_normalization__)return t;const c=typeof t.__sentry_override_normalization_depth__=="number"?t.__sentry_override_normalization_depth__:n;if(c===0)return a.replace("object ","");if(o(t))return"[Circular ~]";const u=t;if(u&&typeof u.toJSON=="function")try{return Jr("",u.toJSON(),c-1,r,s)}catch{}const l=Array.isArray(t)?[]:{};let p=0;const d=Di(t);for(const f in d){if(!Object.prototype.hasOwnProperty.call(d,f))continue;if(p>=r){l[f]="[MaxProperties ~]";break}const h=d[f];l[f]=Jr(f,h,c-1,r,s),p++}return i(t),l}function It(e,t=[]){return[e,t]}function tu(e,t){const[n,r]=e;return[n,[...r,t]]}function uo(e,t){const n=e[1];for(const r of n)if(t(r,r[0].type))return!0;return!1}function Gr(e){const t=er(q);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}function nu(e){const[t,n]=e;let r=JSON.stringify(t);function s(o){typeof r=="string"?r=typeof o=="string"?r+o:[Gr(r),o]:r.push(typeof o=="string"?Gr(o):o)}for(const o of n){const[i,a]=o;if(s(`
${JSON.stringify(i)}
`),typeof a=="string"||a instanceof Uint8Array)s(a);else{let c;try{c=JSON.stringify(a)}catch{c=JSON.stringify(Ie(a))}s(c)}}return typeof r=="string"?r:function(o){const i=o.reduce((u,l)=>u+l.length,0),a=new Uint8Array(i);let c=0;for(const u of o)a.set(u,c),c+=u.length;return a}(r)}function ru(e){return[{type:"span"},e]}function su(e){const t=typeof e.data=="string"?Gr(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}const ou={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function lo(e){return ou[e]}function Qi(e){if(!(e!=null&&e.sdk))return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function iu(e,t,n,r){const s=Qi(n),o=e.type&&e.type!=="replay_event"?e.type:"event";(function(a,c){c&&(a.sdk=a.sdk||{},a.sdk.name=a.sdk.name||c.name,a.sdk.version=a.sdk.version||c.version,a.sdk.integrations=[...a.sdk.integrations||[],...c.integrations||[]],a.sdk.packages=[...a.sdk.packages||[],...c.packages||[]])})(e,n==null?void 0:n.sdk);const i=function(a,c,u,l){var d;const p=(d=a.sdkProcessingMetadata)==null?void 0:d.dynamicSamplingContext;return{event_id:a.event_id,sent_at:new Date().toISOString(),...c&&{sdk:c},...!!u&&l&&{dsn:nn(l)},...p&&{trace:p}}}(e,s,r,t);return delete e.sdkProcessingMetadata,It(i,[[{type:o},e]])}function fo(e){if(!e||e.length===0)return;const t={};return e.forEach(n=>{const r=n.attributes||{},s=r[rr],o=r[sr];typeof s=="string"&&typeof o=="number"&&(t[n.name]={value:o,unit:s})}),t}class or{constructor(t={}){this._traceId=t.traceId||Be(),this._spanId=t.spanId||gn(),this._startTime=t.startTimestamp||ne(),this._links=t.links,this._attributes={},this.setAttributes({[X]:"manual",[dt]:t.op,...t.attributes}),this._name=t.name,t.parentSpanId&&(this._parentSpanId=t.parentSpanId),"sampled"in t&&(this._sampled=t.sampled),t.endTimestamp&&(this._endTime=t.endTimestamp),this._events=[],this._isStandaloneSpan=t.isStandalone,this._endTime&&this._onSpanEnded()}addLink(t){return this._links?this._links.push(t):this._links=[t],this}addLinks(t){return this._links?this._links.push(...t):this._links=t,this}recordException(t,n){}spanContext(){const{_spanId:t,_traceId:n,_sampled:r}=this;return{spanId:t,traceId:n,traceFlags:r?Ss:0}}setAttribute(t,n){return n===void 0?delete this._attributes[t]:this._attributes[t]=n,this}setAttributes(t){return Object.keys(t).forEach(n=>this.setAttribute(n,t[n])),this}updateStartTime(t){this._startTime=st(t)}setStatus(t){return this._status=t,this}updateName(t){return this._name=t,this.setAttribute(Oe,"custom"),this}end(t){this._endTime||(this._endTime=st(t),function(n){if(!O)return;const{description:r="< unknown name >",op:s="< unknown op >"}=B(n),{spanId:o}=n.spanContext(),i=`[Tracing] Finishing "${s}" ${te(n)===n?"root ":""}span "${r}" with ID ${o}`;x.log(i)}(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[dt],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:Wi(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[X],profile_id:this._attributes[Hr],exclusive_time:this._attributes[en],measurements:fo(this._events),is_segment:this._isStandaloneSpan&&te(this)===this||void 0,segment_id:this._isStandaloneSpan?te(this).spanContext().spanId:void 0,links:zi(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(t,n,r){O&&x.log("[Tracing] Adding an event to span:",t);const s=po(n)?n:r||ne(),o=po(n)?{}:n||{},i={name:t,time:st(s),attributes:o};return this._events.push(i),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const t=H();if(t&&t.emit("spanEnd",this),!(this._isStandaloneSpan||this===te(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(r){const s=H();if(!s)return;const o=r[1];if(!o||o.length===0)return void s.recordDroppedEvent("before_send","span");s.sendEnvelope(r)}(function(r,s){const o=Ge(r[0]),i=s==null?void 0:s.getDsn(),a=s==null?void 0:s.getOptions().tunnel,c={sent_at:new Date().toISOString(),...function(d){return!!d.trace_id&&!!d.public_key}(o)&&{trace:o},...!!a&&i&&{dsn:nn(i)}},u=s==null?void 0:s.getOptions().beforeSendSpan,l=u?d=>{const f=B(d);return u(f)||(zr(),f)}:B,p=[];for(const d of r){const f=l(d);f&&p.push(ru(f))}return It(c,p)}([this],t)):(O&&x.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),t&&t.recordDroppedEvent("sample_rate","span")));const n=this._convertSpanToTransaction();n&&(zn(this).scope||W()).captureEvent(n)}_convertSpanToTransaction(){var c;if(!mo(B(this)))return;this._name||(O&&x.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:t,isolationScope:n}=zn(this),r=(c=t==null?void 0:t.getScopeData().sdkProcessingMetadata)==null?void 0:c.normalizedRequest;if(this._sampled!==!0)return;const s=Rn(this).filter(u=>u!==this&&!function(l){return l instanceof or&&l.isStandaloneSpan()}(u)).map(u=>B(u)).filter(mo),o=this._attributes[Oe];delete this._attributes[Vs],s.forEach(u=>{delete u.data[Vs]});const i={contexts:{trace:Kc(this)},spans:s.length>1e3?s.sort((u,l)=>u.start_timestamp-l.start_timestamp).slice(0,1e3):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:t,capturedSpanIsolationScope:n,dynamicSamplingContext:Ge(this)},request:r,...o&&{transaction_info:{source:o}}},a=fo(this._events);return a&&Object.keys(a).length&&(O&&x.log("[Measurements] Adding measurements to transaction event",JSON.stringify(a,void 0,2)),i.measurements=a),i}}function po(e){return e&&typeof e=="number"||e instanceof Date||Array.isArray(e)}function mo(e){return!!(e.start_timestamp&&e.timestamp&&e.span_id&&e.trace_id)}const ea="__SENTRY_SUPPRESS_TRACING__";function ft(e){const t=na();if(t.startInactiveSpan)return t.startInactiveSpan(e);const n=function(o){const i=o.experimental||{},a={isStandalone:i.standalone,...o};if(o.startTime){const c={...a};return c.startTimestamp=st(o.startTime),delete c.startTime,c}return a}(e),{forceTransaction:r,parentSpan:s}=e;return(e.scope?o=>_s(e.scope,o):s!==void 0?o=>ta(s,o):o=>o())(()=>{const o=W(),i=function(a){const c=Hn(a);if(!c)return;const u=H();return(u?u.getOptions():{}).parentSpanIsAlwaysRootSpan?te(c):c}(o);return e.onlyIfParent&&!i?new ct:function({parentSpan:a,spanArguments:c,forceTransaction:u,scope:l}){if(!Je()){const f=new ct;return(u||!a)&&Nn(f,{sampled:"false",sample_rate:"0",transaction:c.name,...Ge(f)}),f}const p=Ye();let d;if(a&&!u)d=function(f,h,m){const{spanId:g,traceId:v}=f.spanContext(),_=!h.getScopeData().sdkProcessingMetadata[ea]&&ot(f),S=_?new or({...m,parentSpanId:g,traceId:v,sampled:_}):new ct({traceId:v});eo(f,S);const y=H();return y&&(y.emit("spanStart",S),m.endTimestamp&&y.emit("spanEnd",S)),S}(a,l,c),eo(a,d);else if(a){const f=Ge(a),{traceId:h,spanId:m}=a.spanContext(),g=ot(a);d=ho({traceId:h,parentSpanId:m,...c},l,g),Nn(d,f)}else{const{traceId:f,dsc:h,parentSpanId:m,sampled:g}={...p.getPropagationContext(),...l.getPropagationContext()};d=ho({traceId:f,parentSpanId:m,...c},l,g),h&&Nn(d,h)}return function(f){if(!O)return;const{description:h="< unknown name >",op:m="< unknown op >",parent_span_id:g}=B(f),{spanId:v}=f.spanContext(),_=ot(f),S=te(f),y=S===f,b=`[Tracing] Starting ${_?"sampled":"unsampled"} ${y?"root ":""}span`,T=[`op: ${m}`,`name: ${h}`,`ID: ${v}`];if(g&&T.push(`parent ID: ${g}`),!y){const{op:w,description:L}=B(S);T.push(`root ID: ${S.spanContext().spanId}`),w&&T.push(`root op: ${w}`),L&&T.push(`root description: ${L}`)}x.log(`${b}
  ${T.join(`
  `)}`)}(d),function(f,h,m){f&&(de(f,Ui,m),de(f,Fi,h))}(d,l,p),d}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:o})})}function ta(e,t){const n=na();return n.withActiveSpan?n.withActiveSpan(e,t):_s(r=>(Qt(r,e||void 0),t(r)))}function na(){return Nt(gt())}function ho(e,t,n){var h;const r=H(),s=(r==null?void 0:r.getOptions())||{},{name:o=""}=e,i={spanAttributes:{...e.attributes},spanName:o,parentSampled:n};r==null||r.emit("beforeSampling",i,{decision:!1});const a=i.parentSampled??n,c=i.spanAttributes,u=t.getPropagationContext(),[l,p,d]=t.getScopeData().sdkProcessingMetadata[ea]?[!1]:function(m,g,v){if(!Je(m))return[!1];let _,S;typeof m.tracesSampler=="function"?(S=m.tracesSampler({...g,inheritOrSampleWith:T=>typeof g.parentSampleRate=="number"?g.parentSampleRate:typeof g.parentSampled=="boolean"?Number(g.parentSampled):T}),_=!0):g.parentSampled!==void 0?S=g.parentSampled:m.tracesSampleRate!==void 0&&(S=m.tracesSampleRate,_=!0);const y=tn(S);if(y===void 0)return O&&x.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(S)} of type ${JSON.stringify(typeof S)}.`),[!1];if(!y)return O&&x.log("[Tracing] Discarding transaction because "+(typeof m.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,y,_];const b=v<y;return b||O&&x.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(S)})`),[b,y,_]}(s,{name:o,parentSampled:a,attributes:c,parentSampleRate:tn((h=u.dsc)==null?void 0:h.sample_rate)},u.sampleRand),f=new or({...e,attributes:{[Oe]:"custom",[ys]:p!==void 0&&d?p:void 0,...c},sampled:l});return!l&&r&&(O&&x.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",f),f}const Ln={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},au="heartbeatFailed",cu="idleTimeout",uu="finalTimeout",lu="externalFinish";function go(e,t={}){const n=new Map;let r,s=!1,o=lu,i=!t.disableAutoFinish;const a=[],{idleTimeout:c=Ln.idleTimeout,finalTimeout:u=Ln.finalTimeout,childSpanTimeout:l=Ln.childSpanTimeout,beforeSpanEnd:p}=t,d=H();if(!d||!Je()){const y=new ct;return Nn(y,{sample_rate:"0",sampled:"false",...Ge(y)}),y}const f=W(),h=ce(),m=function(y){const b=ft(y);return Qt(W(),b),O&&x.log("[Tracing] Started span is an idle span"),b}(e);function g(){r&&(clearTimeout(r),r=void 0)}function v(y){g(),r=setTimeout(()=>{!s&&n.size===0&&i&&(o=cu,m.end(y))},c)}function _(y){r=setTimeout(()=>{!s&&i&&(o=au,m.end(y))},l)}function S(y){s=!0,n.clear(),a.forEach(E=>E()),Qt(f,h);const b=B(m),{start_timestamp:T}=b;if(!T)return;b.data[Bn]||m.setAttribute(Bn,o),x.log(`[Tracing] Idle span "${b.op}" finished`);const w=Rn(m).filter(E=>E!==m);let L=0;w.forEach(E=>{E.isRecording()&&(E.setStatus({code:Y,message:"cancelled"}),E.end(y),O&&x.log("[Tracing] Cancelling span since span ended early",JSON.stringify(E,void 0,2)));const R=B(E),{timestamp:D=0,start_timestamp:k=0}=R,A=k<=y,I=D-k<=(u+c)/1e3;if(O){const P=JSON.stringify(E,void 0,2);A?I||x.log("[Tracing] Discarding span since it finished after idle span final timeout",P):x.log("[Tracing] Discarding span since it happened after idle span was finished",P)}I&&A||(function(P,$){P[it]&&P[it].delete($)}(m,E),L++)}),L>0&&m.setAttribute("sentry.idle_span_discarded_spans",L)}return m.end=new Proxy(m.end,{apply(y,b,T){if(p&&p(m),b instanceof ct)return;const[w,...L]=T,E=st(w||ne()),R=Rn(m).filter(P=>P!==m);if(!R.length)return S(E),Reflect.apply(y,b,[E,...L]);const D=R.map(P=>B(P).timestamp).filter(P=>!!P),k=D.length?Math.max(...D):void 0,A=B(m).start_timestamp,I=Math.min(A?A+u/1e3:1/0,Math.max(A||-1/0,Math.min(E,k||1/0)));return S(I),Reflect.apply(y,b,[I,...L])}}),a.push(d.on("spanStart",y=>{if(!(s||y===m||B(y).timestamp)){var b;Rn(m).includes(y)&&(b=y.spanContext().spanId,g(),n.set(b,!0),_(ne()+l/1e3))}})),a.push(d.on("spanEnd",y=>{var b;s||(b=y.spanContext().spanId,n.has(b)&&n.delete(b),n.size===0&&v(ne()+c/1e3))})),a.push(d.on("idleSpanEnableAutoFinish",y=>{y===m&&(i=!0,v(),n.size&&_())})),t.disableAutoFinish||v(),setTimeout(()=>{s||(m.setStatus({code:Y,message:"deadline_exceeded"}),o=uu,m.end())},u),m}var Pe;function pt(e){return new Ke(t=>{t(e)})}function Wn(e){return new Ke((t,n)=>{n(e)})}(function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"})(Pe||(Pe={}));class Ke{constructor(t){this._state=Pe.PENDING,this._handlers=[],this._runExecutor(t)}then(t,n){return new Ke((r,s)=>{this._handlers.push([!1,o=>{if(t)try{r(t(o))}catch(i){s(i)}else r(o)},o=>{if(n)try{r(n(o))}catch(i){s(i)}else s(o)}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new Ke((n,r)=>{let s,o;return this.then(i=>{o=!1,s=i,t&&t()},i=>{o=!0,s=i,t&&t()}).then(()=>{o?r(s):n(s)})})}_executeHandlers(){if(this._state===Pe.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===Pe.RESOLVED&&n[1](this._value),this._state===Pe.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(t){const n=(o,i)=>{this._state===Pe.PENDING&&(nr(i)?i.then(r,s):(this._state=o,this._value=i,this._executeHandlers()))},r=o=>{n(Pe.RESOLVED,o)},s=o=>{n(Pe.REJECTED,o)};try{t(r,s)}catch(o){s(o)}}}function Kr(e,t,n,r=0){return new Ke((s,o)=>{const i=e[r];if(t===null||typeof i!="function")s(t);else{const a=i({...t},n);O&&i.id&&a===null&&x.log(`Event processor "${i.id}" dropped event`),nr(a)?a.then(c=>Kr(e,c,n,r+1).then(s)).then(null,o):Kr(e,a,n,r+1).then(s).then(null,o)}})}let xn,vo,yr;function du(e,t){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:o}=t;(function(i,a){const{extra:c,tags:u,user:l,contexts:p,level:d,transactionName:f}=a;Object.keys(c).length&&(i.extra={...c,...i.extra}),Object.keys(u).length&&(i.tags={...u,...i.tags}),Object.keys(l).length&&(i.user={...l,...i.user}),Object.keys(p).length&&(i.contexts={...p,...i.contexts}),d&&(i.level=d),f&&i.type!=="transaction"&&(i.transaction=f)})(e,t),r&&function(i,a){i.contexts={trace:Vc(a),...i.contexts},i.sdkProcessingMetadata={dynamicSamplingContext:Ge(a),...i.sdkProcessingMetadata};const c=te(a),u=B(c).description;u&&!i.transaction&&i.type==="transaction"&&(i.transaction=u)}(e,r),function(i,a){i.fingerprint=i.fingerprint?Array.isArray(i.fingerprint)?i.fingerprint:[i.fingerprint]:[],a&&(i.fingerprint=i.fingerprint.concat(a)),i.fingerprint.length||delete i.fingerprint}(e,n),function(i,a){const c=[...i.breadcrumbs||[],...a];i.breadcrumbs=c.length?c:void 0}(e,s),function(i,a){i.sdkProcessingMetadata={...i.sdkProcessingMetadata,...a}}(e,o)}function _o(e,t){const{extra:n,tags:r,user:s,contexts:o,level:i,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:l,attachments:p,propagationContext:d,transactionName:f,span:h}=t;Tn(e,"extra",n),Tn(e,"tags",r),Tn(e,"user",s),Tn(e,"contexts",o),e.sdkProcessingMetadata=hn(e.sdkProcessingMetadata,a,2),i&&(e.level=i),f&&(e.transactionName=f),h&&(e.span=h),c.length&&(e.breadcrumbs=[...e.breadcrumbs,...c]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),l.length&&(e.eventProcessors=[...e.eventProcessors,...l]),p.length&&(e.attachments=[...e.attachments,...p]),e.propagationContext={...e.propagationContext,...d}}function Tn(e,t,n){e[t]=hn(e[t],n,1)}function fu(e,t,n,r,s,o){const{normalizeDepth:i=3,normalizeMaxBreadth:a=1e3}=e,c={...t,event_id:t.event_id||n.event_id||ge(),timestamp:t.timestamp||mn()},u=n.integrations||e.integrations.map(h=>h.name);(function(h,m){const{environment:g,release:v,dist:_,maxValueLength:S=250}=m;h.environment=h.environment||g||ws,!h.release&&v&&(h.release=v),!h.dist&&_&&(h.dist=_);const y=h.request;y!=null&&y.url&&(y.url=Un(y.url,S))})(c,e),function(h,m){m.length>0&&(h.sdk=h.sdk||{},h.sdk.integrations=[...h.sdk.integrations||[],...m])}(c,u),s&&s.emit("applyFrameMetadata",t),t.type===void 0&&function(h,m){var v,_;const g=function(S){const y=q._sentryDebugIds;if(!y)return{};const b=Object.keys(y);return yr&&b.length===vo||(vo=b.length,yr=b.reduce((T,w)=>{xn||(xn={});const L=xn[w];if(L)T[L[0]]=L[1];else{const E=S(w);for(let R=E.length-1;R>=0;R--){const D=E[R],k=D==null?void 0:D.filename,A=y[w];if(k&&A){T[k]=A,xn[w]=[k,A];break}}}return T},{})),yr}(m);(_=(v=h.exception)==null?void 0:v.values)==null||_.forEach(S=>{var y,b;(b=(y=S.stacktrace)==null?void 0:y.frames)==null||b.forEach(T=>{T.filename&&(T.debug_id=g[T.filename])})})}(c,e.stackParser);const l=function(h,m){if(!m)return h;const g=h?h.clone():new Le;return g.update(m),g}(r,n.captureContext);n.mechanism&&Tt(c,n.mechanism);const p=s?s.getEventProcessors():[],d=qn("globalScope",()=>new Le).getScopeData();o&&_o(d,o.getScopeData()),l&&_o(d,l.getScopeData());const f=[...n.attachments||[],...d.attachments];return f.length&&(n.attachments=f),du(c,d),Kr([...p,...d.eventProcessors],c,n).then(h=>(h&&function(m){var _,S;const g={};if((S=(_=m.exception)==null?void 0:_.values)==null||S.forEach(y=>{var b,T;(T=(b=y.stacktrace)==null?void 0:b.frames)==null||T.forEach(w=>{w.debug_id&&(w.abs_path?g[w.abs_path]=w.debug_id:w.filename&&(g[w.filename]=w.debug_id),delete w.debug_id)})}),Object.keys(g).length===0)return;m.debug_meta=m.debug_meta||{},m.debug_meta.images=m.debug_meta.images||[];const v=m.debug_meta.images;Object.entries(g).forEach(([y,b])=>{v.push({type:"sourcemap",code_file:y,debug_id:b})})}(h),typeof i=="number"&&i>0?function(m,g,v){var S,y;if(!m)return null;const _={...m,...m.breadcrumbs&&{breadcrumbs:m.breadcrumbs.map(b=>({...b,...b.data&&{data:Ie(b.data,g,v)}}))},...m.user&&{user:Ie(m.user,g,v)},...m.contexts&&{contexts:Ie(m.contexts,g,v)},...m.extra&&{extra:Ie(m.extra,g,v)}};return(S=m.contexts)!=null&&S.trace&&_.contexts&&(_.contexts.trace=m.contexts.trace,m.contexts.trace.data&&(_.contexts.trace.data=Ie(m.contexts.trace.data,g,v))),m.spans&&(_.spans=m.spans.map(b=>({...b,...b.data&&{data:Ie(b.data,g,v)}}))),(y=m.contexts)!=null&&y.flags&&_.contexts&&(_.contexts.flags=Ie(m.contexts.flags,3,v)),_}(h,i,a):h))}function yo(e,t){return W().captureEvent(e,t)}function bo(e){const t=Ye(),n=W(),{userAgent:r}=q.navigator||{},s=Mc({user:n.getUser()||t.getUser(),...r&&{userAgent:r},...e}),o=t.getSession();return(o==null?void 0:o.status)==="ok"&&kt(o,{status:"exited"}),ra(),t.setSession(s),s}function ra(){const e=Ye(),t=W().getSession()||e.getSession();t&&function(n,r){let s={};n.status==="ok"&&(s={status:"exited"}),kt(n,s)}(t),sa(),e.setSession()}function sa(){const e=Ye(),t=H(),n=e.getSession();n&&t&&t.captureSession(n)}function So(e=!1){e?ra():sa()}const pu="7";function mu(e,t,n){return t||`${function(r){return`${function(s){const o=s.protocol?`${s.protocol}:`:"",i=s.port?`:${s.port}`:"";return`${o}//${s.host}${i}${s.path?`/${s.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(e)}?${function(r,s){const o={sentry_version:pu};return r.publicKey&&(o.sentry_key=r.publicKey),s&&(o.sentry_client=`${s.name}/${s.version}`),new URLSearchParams(o).toString()}(e,n)}`}const wo=[];function hu(e){const t=e.defaultIntegrations||[],n=e.integrations;let r;if(t.forEach(s=>{s.isDefaultInstance=!0}),Array.isArray(n))r=[...t,...n];else if(typeof n=="function"){const s=n(t);r=Array.isArray(s)?s:[s]}else r=t;return function(s){const o={};return s.forEach(i=>{const{name:a}=i,c=o[a];c&&!c.isDefaultInstance&&i.isDefaultInstance||(o[a]=i)}),Object.values(o)}(r)}function Eo(e,t){for(const n of t)n!=null&&n.afterAllSetup&&n.afterAllSetup(e)}function xo(e,t,n){if(n[t.name])O&&x.log(`Integration skipped because it was already installed: ${t.name}`);else{if(n[t.name]=t,wo.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),wo.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(s,o)=>r(s,o,e))}if(typeof t.processEvent=="function"){const r=t.processEvent.bind(t),s=Object.assign((o,i)=>r(o,i,e),{id:t.name});e.addEventProcessor(s)}O&&x.log(`Integration installed: ${t.name}`)}}function oa(e){const t=[];e.message&&t.push(e.message);try{const n=e.exception.values[e.exception.values.length-1];n!=null&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`))}catch{}return t}const To="Not capturing exception because it's already been captured.",ko="Discarded session because of missing or non-string release",ia=Symbol.for("SentryInternalError"),aa=Symbol.for("SentryDoNotSendEventError");function kn(e){return{message:e,[ia]:!0}}function br(e){return{message:e,[aa]:!0}}function $o(e){return!!e&&typeof e=="object"&&ia in e}function Io(e){return!!e&&typeof e=="object"&&aa in e}class gu{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=eu(t.dsn):O&&x.warn("No DSN provided, client will not send events."),this._dsn){const n=mu(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,r){const s=ge();if(Gs(t))return O&&x.log(To),s;const o={event_id:s,...n};return this._process(this.eventFromException(t,o).then(i=>this._captureEvent(i,o,r))),o.event_id}captureMessage(t,n,r,s){const o={event_id:ge(),...r},i=hs(t)?t:String(t),a=Xt(t)?this.eventFromMessage(i,n,o):this.eventFromException(t,o);return this._process(a.then(c=>this._captureEvent(c,o,s))),o.event_id}captureEvent(t,n,r){const s=ge();if(n!=null&&n.originalException&&Gs(n.originalException))return O&&x.log(To),s;const o={event_id:s,...n},i=t.sdkProcessingMetadata||{},a=i.capturedSpanScope,c=i.capturedSpanIsolationScope;return this._process(this._captureEvent(t,o,a||r,c)),o.event_id}captureSession(t){this.sendSession(t),kt(t,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(r=>n.flush(t).then(s=>r&&s))):pt(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];xo(this,t,this._integrations),n||Eo(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let r=iu(t,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=tu(r,su(o));const s=this.sendEnvelope(r);s&&s.then(o=>this.emit("afterSendEvent",t,o),null)}sendSession(t){const{release:n,environment:r=ws}=this._options;if("aggregates"in t){const o=t.attrs||{};if(!o.release&&!n)return void(O&&x.warn(ko));o.release=o.release||n,o.environment=o.environment||r,t.attrs=o}else{if(!t.release&&!n)return void(O&&x.warn(ko));t.release=t.release||n,t.environment=t.environment||r}this.emit("beforeSendSession",t);const s=function(o,i,a,c){const u=Qi(a);return It({sent_at:new Date().toISOString(),...u&&{sdk:u},...!!c&&i&&{dsn:nn(i)}},["aggregates"in o?[{type:"sessions"},o]:[{type:"session"},o.toJSON()]])}(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(t,n,r=1){if(this._options.sendClientReports){const s=`${t}:${n}`;O&&x.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(t,n){const r=this._hooks[t]=this._hooks[t]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(t,...n){const r=this._hooks[t];r&&r.forEach(s=>s(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(O&&x.error("Error while sending envelope:",n),n)):(O&&x.error("Transport disabled"),pt({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=function(n,r){const s={};return r.forEach(o=>{o&&xo(n,o,s)}),s}(this,t),Eo(this,t)}_updateSessionFromEvent(t,n){var a;let r=n.level==="fatal",s=!1;const o=(a=n.exception)==null?void 0:a.values;if(o){s=!0;for(const c of o){const u=c.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const i=t.status==="ok";(i&&t.errors===0||i&&r)&&(kt(t,{...r&&{status:"crashed"},errors:t.errors||Number(s||r)}),this.captureSession(t))}_isClientDoneProcessing(t){return new Ke(n=>{let r=0;const s=setInterval(()=>{this._numProcessing==0?(clearInterval(s),n(!0)):(r+=1,t&&r>=t&&(clearInterval(s),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,r,s){const o=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&(i!=null&&i.length)&&(n.integrations=i),this.emit("preprocessEvent",t,n),t.type||s.setLastEventId(t.event_id||n.event_id),fu(o,t,n,r,this,s).then(a=>{if(a===null)return a;this.emit("postprocessEvent",a,n),a.contexts={trace:Uc(r),...a.contexts};const c=Xi(this,r);return a.sdkProcessingMetadata={dynamicSamplingContext:c,...a.sdkProcessingMetadata},a})}_captureEvent(t,n={},r=W(),s=Ye()){return O&&Sr(t)&&x.log(`Captured error event \`${oa(t)[0]||"<unknown>"}\``),this._processEvent(t,n,r,s).then(o=>o.event_id,o=>{O&&(Io(o)?x.log(o.message):$o(o)?x.warn(o.message):x.warn(o))})}_processEvent(t,n,r,s){const o=this.getOptions(),{sampleRate:i}=o,a=Po(t),c=Sr(t),u=t.type||"error",l=`before send for type \`${u}\``,p=i===void 0?void 0:tn(i);if(c&&typeof p=="number"&&Math.random()>p)return this.recordDroppedEvent("sample_rate","error"),Wn(br(`Discarding event because it's not included in the random sample (sampling rate = ${i})`));const d=u==="replay_event"?"replay":u;return this._prepareEvent(t,n,r,s).then(f=>{if(f===null)throw this.recordDroppedEvent("event_processor",d),br("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return f;const h=function(m,g,v,_){const{beforeSend:S,beforeSendTransaction:y,beforeSendSpan:b}=g;let T=v;if(Sr(T)&&S)return S(T,_);if(Po(T)){if(b){const L=b(function(E){var C;const{trace_id:R,parent_span_id:D,span_id:k,status:A,origin:I,data:P,op:$}=((C=E.contexts)==null?void 0:C.trace)??{};return{data:P??{},description:E.transaction,op:$,parent_span_id:D,span_id:k??"",start_timestamp:E.start_timestamp??0,status:A,timestamp:E.timestamp,trace_id:R??"",origin:I,profile_id:P==null?void 0:P[Hr],exclusive_time:P==null?void 0:P[en],measurements:E.measurements,is_segment:!0}}(T));if(L?T=hn(v,{type:"transaction",timestamp:(w=L).timestamp,start_timestamp:w.start_timestamp,transaction:w.description,contexts:{trace:{trace_id:w.trace_id,span_id:w.span_id,parent_span_id:w.parent_span_id,op:w.op,status:w.status,origin:w.origin,data:{...w.data,...w.profile_id&&{[Hr]:w.profile_id},...w.exclusive_time&&{[en]:w.exclusive_time}}}},measurements:w.measurements}):zr(),T.spans){const E=[];for(const R of T.spans){const D=b(R);D?E.push(D):(zr(),E.push(R))}T.spans=E}}if(y){if(T.spans){const L=T.spans.length;T.sdkProcessingMetadata={...v.sdkProcessingMetadata,spanCountBeforeProcessing:L}}return y(T,_)}}var w;return T}(0,o,f,n);return function(m,g){const v=`${g} must return \`null\` or a valid event.`;if(nr(m))return m.then(_=>{if(!Zt(_)&&_!==null)throw kn(v);return _},_=>{throw kn(`${g} rejected with ${_}`)});if(!Zt(m)&&m!==null)throw kn(v);return m}(h,l)}).then(f=>{var g;if(f===null){if(this.recordDroppedEvent("before_send",d),a){const v=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",v)}throw br(`${l} returned \`null\`, will not send event.`)}const h=r.getSession()||s.getSession();if(c&&h&&this._updateSessionFromEvent(h,f),a){const v=(((g=f.sdkProcessingMetadata)==null?void 0:g.spanCountBeforeProcessing)||0)-(f.spans?f.spans.length:0);v>0&&this.recordDroppedEvent("before_send","span",v)}const m=f.transaction_info;if(a&&m&&f.transaction!==t.transaction){const v="custom";f.transaction_info={...m,source:v}}return this.sendEvent(f,n),f}).then(null,f=>{throw Io(f)||$o(f)?f:(this.captureException(f,{data:{__sentry__:!0},originalException:f}),kn(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${f}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,r])=>{const[s,o]=n.split(":");return{reason:s,category:o,quantity:r}})}_flushOutcomes(){O&&x.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0)return void(O&&x.log("No outcomes to send"));if(!this._dsn)return void(O&&x.log("No dsn provided, will not send outcomes"));O&&x.log("Sending outcomes:",t);const n=(r=t,It((s=this._options.tunnel&&nn(this._dsn))?{dsn:s}:{},[[{type:"client_report"},{timestamp:mn(),discarded_events:r}]]));var r,s;this.sendEnvelope(n)}}function Sr(e){return e.type===void 0}function Po(e){return e.type==="transaction"}function wr(e,t){var o;const n=function(i){var a;return(a=q._sentryClientToLogBufferMap)==null?void 0:a.get(i)}(e)??[];if(n.length===0)return;const r=e.getOptions(),s=function(i,a,c,u){const l={};return a!=null&&a.sdk&&(l.sdk={name:a.sdk.name,version:a.sdk.version}),c&&u&&(l.dsn=nn(u)),It(l,[(p=i,[{type:"log",item_count:p.length,content_type:"application/vnd.sentry.items.log+json"},{items:p}])]);var p}(n,r._metadata,r.tunnel,e.getDsn());(o=q._sentryClientToLogBufferMap)==null||o.set(e,[]),e.emit("flushLogs"),e.sendEnvelope(s)}function vu(e,t){t.debug===!0&&(O?x.enable():vt(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),W().update(t.initialScope);const n=new e(t);return function(r){W().setClient(r)}(n),n.init(),n}q._sentryClientToLogBufferMap=new WeakMap;const ca=Symbol.for("SentryBufferFullError");function _u(e){const t=[];function n(r){return t.splice(t.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(r){if(!(e===void 0||t.length<e))return Wn(ca);const s=r();return t.indexOf(s)===-1&&t.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s},drain:function(r){return new Ke((s,o)=>{let i=t.length;if(!i)return s(!0);const a=setTimeout(()=>{r&&r>0&&s(!1)},r);t.forEach(c=>{pt(c).then(()=>{--i||(clearTimeout(a),s(!0))},o)})})}}}const yu=6e4;function bu(e,{statusCode:t,headers:n},r=Date.now()){const s={...e},o=n==null?void 0:n["x-sentry-rate-limits"],i=n==null?void 0:n["retry-after"];if(o)for(const a of o.trim().split(",")){const[c,u,,,l]=a.split(":",5),p=parseInt(c,10),d=1e3*(isNaN(p)?60:p);if(u)for(const f of u.split(";"))f==="metric_bucket"&&l&&!l.split(";").includes("custom")||(s[f]=r+d);else s.all=r+d}else i?s.all=r+function(a,c=Date.now()){const u=parseInt(`${a}`,10);if(!isNaN(u))return 1e3*u;const l=Date.parse(`${a}`);return isNaN(l)?yu:l-c}(i,r):t===429&&(s.all=r+6e4);return s}const Su=64;function wu(e,t,n=_u(e.bufferSize||Su)){let r={};return{send:function(s){const o=[];if(uo(s,(c,u)=>{const l=lo(u);(function(p,d,f=Date.now()){return function(h,m){return h[m]||h.all||0}(p,d)>f})(r,l)?e.recordDroppedEvent("ratelimit_backoff",l):o.push(c)}),o.length===0)return pt({});const i=It(s[0],o),a=c=>{uo(i,(u,l)=>{e.recordDroppedEvent(c,lo(l))})};return n.add(()=>t({body:nu(i)}).then(c=>(c.statusCode!==void 0&&(c.statusCode<200||c.statusCode>=300)&&O&&x.warn(`Sentry responded with status code ${c.statusCode} to sent event.`),r=bu(r,c),c),c=>{throw a("network_error"),O&&x.error("Encountered error running transport request:",c),c})).then(c=>c,c=>{if(c===ca)return O&&x.error("Skipped sending event because buffer is full."),a("queue_overflow"),pt({});throw c})},flush:s=>n.drain(s)}}function Eu(e){var t;((t=e.user)==null?void 0:t.ip_address)===void 0&&(e.user={...e.user,ip_address:"{{auto}}"})}function xu(e){var t;"aggregates"in e?((t=e.attrs)==null?void 0:t.ip_address)===void 0&&(e.attrs={...e.attrs,ip_address:"{{auto}}"}):e.ipAddress===void 0&&(e.ipAddress="{{auto}}")}function ua(e,t,n=[t],r="npm"){const s=e._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${t}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:rt})),version:rt}),e._metadata=s}function la(e={}){const t=H();if(!function(){const a=H();return(a==null?void 0:a.getOptions().enabled)!==!1&&!!(a!=null&&a.getTransport())}()||!t)return{};const n=Nt(gt());if(n.getTraceData)return n.getTraceData(e);const r=W(),s=e.span||ce(),o=s?function(a){const{traceId:c,spanId:u}=a.spanContext();return Xs(c,u,ot(a))}(s):function(a){const{traceId:c,sampled:u,propagationSpanId:l}=a.getPropagationContext();return Xs(c,l,u)}(r),i=Jc(s?Ge(s):Xi(t,r));return Bi.test(o)?{"sentry-trace":o,baggage:i}:(x.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const Tu=100;function Qe(e,t){const n=H(),r=Ye();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:o=Tu}=n.getOptions();if(o<=0)return;const i={timestamp:mn(),...e},a=s?vt(()=>s(i,t)):i;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,t),r.addBreadcrumb(a,o))}let Ao;const Oo=new WeakMap,ku=()=>({name:"FunctionToString",setupOnce(){Ao=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=vs(this),n=Oo.has(H())&&t!==void 0?t:this;return Ao.apply(n,e)}}catch{}},setup(e){Oo.set(e,!0)}}),$u=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],Iu=(e={})=>{let t;return{name:"EventFilters",setup(n){const r=n.getOptions();t=Co(e,r)},processEvent(n,r,s){if(!t){const o=s.getOptions();t=Co(e,o)}return function(o,i){if(o.type){if(o.type==="transaction"&&function(a,c){if(!(c!=null&&c.length))return!1;const u=a.transaction;return!!u&&nt(u,c)}(o,i.ignoreTransactions))return O&&x.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${et(o)}`),!0}else{if(function(a,c){return c!=null&&c.length?oa(a).some(u=>nt(u,c)):!1}(o,i.ignoreErrors))return O&&x.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${et(o)}`),!0;if(function(a){var c,u;return(u=(c=a.exception)==null?void 0:c.values)!=null&&u.length?!a.message&&!a.exception.values.some(l=>l.stacktrace||l.type&&l.type!=="Error"||l.value):!1}(o))return O&&x.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${et(o)}`),!0;if(function(a,c){if(!(c!=null&&c.length))return!1;const u=$n(a);return!!u&&nt(u,c)}(o,i.denyUrls))return O&&x.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${et(o)}.
Url: ${$n(o)}`),!0;if(!function(a,c){if(!(c!=null&&c.length))return!0;const u=$n(a);return!u||nt(u,c)}(o,i.allowUrls))return O&&x.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${et(o)}.
Url: ${$n(o)}`),!0}return!1}(n,t)?null:n}}},Pu=(e={})=>({...Iu(e),name:"InboundFilters"});function Co(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:$u],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]]}}function $n(e){var t,n;try{const r=[...((t=e.exception)==null?void 0:t.values)??[]].reverse().find(o=>{var i,a,c;return((i=o.mechanism)==null?void 0:i.parent_id)===void 0&&((c=(a=o.stacktrace)==null?void 0:a.frames)==null?void 0:c.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?function(o=[]){for(let i=o.length-1;i>=0;i--){const a=o[i];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}(s):null}catch{return O&&x.error(`Cannot extract url for event ${et(e)}`),null}}function Au(e,t,n,r,s,o){var a;if(!((a=s.exception)!=null&&a.values)||!o||!Ne(o.originalException,Error))return;const i=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;i&&(s.exception.values=Vr(e,t,r,o.originalException,n,s.exception.values,i,0))}function Vr(e,t,n,r,s,o,i,a){if(o.length>=n+1)return o;let c=[...o];if(Ne(r[s],Error)){Ro(i,a);const u=e(t,r[s]),l=c.length;Do(u,s,l,a),c=Vr(e,t,n,r[s],s,[u,...c],u,l)}return Array.isArray(r.errors)&&r.errors.forEach((u,l)=>{if(Ne(u,Error)){Ro(i,a);const p=e(t,u),d=c.length;Do(p,`errors[${l}]`,d,a),c=Vr(e,t,n,u,s,[p,...c],p,d)}}),c}function Ro(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function Do(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function Ou(){"console"in q&&qr.forEach(function(e){e in q.console&&ue(q.console,e,function(t){return Fn[e]=t,function(...n){ve("console",{args:n,level:e});const r=Fn[e];r==null||r.apply(q.console,n)}})})}function Cu(e){return e==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log"}const Ru=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{if(function(n,r){return r?!!(function(s,o){const i=s.message,a=o.message;return!(!i&&!a||i&&!a||!i&&a||i!==a||!Lo(s,o)||!No(s,o))}(n,r)||function(s,o){const i=Mo(o),a=Mo(s);return!(!i||!a||i.type!==a.type||i.value!==a.value||!Lo(s,o)||!No(s,o))}(n,r)):!1}(t,e))return O&&x.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}};function No(e,t){let n=oo(e),r=oo(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const o=r[s],i=n[s];if(o.filename!==i.filename||o.lineno!==i.lineno||o.colno!==i.colno||o.function!==i.function)return!1}return!0}function Lo(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function Mo(e){var t;return((t=e.exception)==null?void 0:t.values)&&e.exception.values[0]}const Du="thismessage:/";function da(e){return"isRelative"in e}function fa(e,t){const n=e.indexOf("://")<=0&&e.indexOf("//")!==0,r=n?Du:void 0;try{if("canParse"in URL&&!URL.canParse(e,r))return;const s=new URL(e,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function Nu(e){if(da(e))return e.pathname;const t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}function xt(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}function Lu(e,t,n,r,s="auto.http.browser"){if(!e.fetchData)return;const{method:o,url:i}=e.fetchData,a=Je()&&t(i);if(e.endTimestamp&&a){const p=e.fetchData.__span;if(!p)return;const d=r[p];return void(d&&(function(f,h){var m;if(h.response){qi(f,h.response.status);const g=((m=h.response)==null?void 0:m.headers)&&h.response.headers.get("content-length");if(g){const v=parseInt(g);v>0&&f.setAttribute("http.response_content_length",v)}}else h.error&&f.setStatus({code:Y,message:"internal_error"});f.end()}(d,e),delete r[p]))}const c=!!ce(),u=a&&c?ft(function(p,d,f){const h=fa(p);return{name:h?`${d} ${Nu(h)}`:d,attributes:Mu(p,h,d,f)}}(i,o,s)):new ct;if(e.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(e.fetchData.url)){const p=e.args[0],d=e.args[1]||{},f=function(h,m,g){const v=la({span:g}),_=v["sentry-trace"],S=v.baggage;if(!_)return;const y=m.headers||(Oi(h)?h.headers:void 0);if(y){if(function(b){return typeof Headers<"u"&&Ne(b,Headers)}(y)){const b=new Headers(y);if(b.get("sentry-trace")||b.set("sentry-trace",_),S){const T=b.get("baggage");T?In(T)||b.set("baggage",`${T},${S}`):b.set("baggage",S)}return b}if(Array.isArray(y)){const b=[...y];y.find(w=>w[0]==="sentry-trace")||b.push(["sentry-trace",_]);const T=y.find(w=>w[0]==="baggage"&&In(w[1]));return S&&!T&&b.push(["baggage",S]),b}{const b="sentry-trace"in y?y["sentry-trace"]:void 0,T="baggage"in y?y.baggage:void 0,w=T?Array.isArray(T)?[...T]:[T]:[],L=T&&(Array.isArray(T)?T.find(E=>In(E)):In(T));return S&&!L&&w.push(S),{...y,"sentry-trace":b??_,baggage:w.length>0?w.join(","):void 0}}}return{...v}}(p,d,Je()&&c?u:void 0);f&&(e.args[1]=d,d.headers=f)}const l=H();if(l){const p={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};l.emit("beforeOutgoingRequestSpan",u,p)}return u}function In(e){return e.split(",").some(t=>t.trim().startsWith(bs))}function Mu(e,t,n,r){const s={url:e,type:"fetch","http.method":n,[X]:r,[dt]:"http.client"};return t&&(da(t)||(s["http.url"]=t.href,s["server.address"]=t.host),t.search&&(s["http.query"]=t.search),t.hash&&(s["http.fragment"]=t.hash)),s}function jo(e){return e===void 0?void 0:e>=400&&e<500?"warning":e>=500?"error":void 0}const rn=q;function pa(){if(!("fetch"in rn))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Yr(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function ma(e,t){const n="fetch";ze(n,e),We(n,()=>ha(void 0,t))}function ha(e,t=!1){t&&!function(){var s;if(typeof EdgeRuntime=="string")return!0;if(!pa())return!1;if(Yr(rn.fetch))return!0;let n=!1;const r=rn.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(s=o.contentWindow)!=null&&s.fetch&&(n=Yr(o.contentWindow.fetch)),r.head.removeChild(o)}catch(o){O&&x.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",o)}return n}()||ue(q,"fetch",function(n){return function(...r){const s=new Error,{method:o,url:i}=function(c){if(c.length===0)return{method:"GET",url:""};if(c.length===2){const[l,p]=c;return{url:qo(l),method:Xr(p,"method")?String(p.method).toUpperCase():"GET"}}const u=c[0];return{url:qo(u),method:Xr(u,"method")?String(u.method).toUpperCase():"GET"}}(r),a={args:r,fetchData:{method:o,url:i},startTimestamp:1e3*ne(),virtualError:s,headers:qu(r)};return e||ve("fetch",{...a}),n.apply(q,r).then(async c=>(e?e(c):ve("fetch",{...a,endTimestamp:1e3*ne(),response:c}),c),c=>{if(ve("fetch",{...a,endTimestamp:1e3*ne(),error:c}),ms(c)&&c.stack===void 0&&(c.stack=s.stack,de(c,"framesToPop",1)),c instanceof TypeError&&(c.message==="Failed to fetch"||c.message==="Load failed"||c.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(a.fetchData.url);c.message=`${c.message} (${u.host})`}catch{}throw c})}})}function ju(e){let t;try{t=e.clone()}catch{return}(async function(n,r){if(n!=null&&n.body){const s=n.body,o=s.getReader(),i=setTimeout(()=>{s.cancel().then(null,()=>{})},9e4);let a=!0;for(;a;){let c;try{c=setTimeout(()=>{s.cancel().then(null,()=>{})},5e3);const{done:u}=await o.read();clearTimeout(c),u&&(r(),a=!1)}catch{a=!1}finally{clearTimeout(c)}}clearTimeout(i),o.releaseLock(),s.cancel().then(null,()=>{})}})(t,()=>{ve("fetch-body-resolved",{endTimestamp:1e3*ne(),response:e})})}function Xr(e,t){return!!e&&typeof e=="object"&&!!e[t]}function qo(e){return typeof e=="string"?e:e?Xr(e,"url")?e.url:e.toString?e.toString():"":""}function qu(e){const[t,n]=e;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(Oi(t))return new Headers(t.headers)}catch{}}const j=q;let Zr=0;function Fo(){return Zr>0}function Pt(e,t={}){if(!function(r){return typeof r=="function"}(e))return e;try{const r=e.__sentry_wrapped__;if(r)return typeof r=="function"?r:e;if(vs(e))return e}catch{return e}const n=function(...r){try{const s=r.map(o=>Pt(o,t));return e.apply(this,s)}catch(s){throw Zr++,setTimeout(()=>{Zr--}),_s(o=>{var i;o.addEventProcessor(a=>(t.mechanism&&(Fr(a,void 0),Tt(a,t.mechanism)),a.extra={...a.extra,arguments:r},a)),i=s,W().captureException(i,void 0)}),s}};try{for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}catch{}Ri(n,e),de(e,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>e.name})}catch{}return n}function Qr(){const e=pn(),{referrer:t}=j.document||{},{userAgent:n}=j.navigator||{};return{url:e,headers:{...t&&{Referer:t},...n&&{"User-Agent":n}}}}function Es(e,t){const n=xs(e,t),r={type:Hu(t),value:Bu(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Fu(e,t,n,r){const s=H(),o=s==null?void 0:s.getOptions().normalizeDepth,i=function(u){for(const l in u)if(Object.prototype.hasOwnProperty.call(u,l)){const p=u[l];if(p instanceof Error)return p}}(t),a={__serialized__:Zi(t,o)};if(i)return{exception:{values:[Es(e,i)]},extra:a};const c={exception:{values:[{type:tr(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:zu(t,{isUnhandledRejection:r})}]},extra:a};if(n){const u=xs(e,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function Er(e,t){return{exception:{values:[Es(e,t)]}}}function xs(e,t){const n=t.stacktrace||t.stack||"",r=function(o){return o&&Uu.test(o.message)?1:0}(t),s=function(o){return typeof o.framesToPop=="number"?o.framesToPop:0}(t);try{return e(n,r,s)}catch{}return[]}const Uu=/Minified React error #\d+;/i;function ga(e){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&e instanceof WebAssembly.Exception}function Hu(e){const t=e==null?void 0:e.name;return!t&&ga(e)?e.message&&Array.isArray(e.message)&&e.message.length==2?e.message[0]:"WebAssembly.Exception":t}function Bu(e){const t=e==null?void 0:e.message;return ga(e)?Array.isArray(e.message)&&e.message.length==2?e.message[1]:"wasm exception":t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function es(e,t,n,r,s){let o;if(Pi(t)&&t.error)return Er(e,t.error);if(Bs(t)||Dt(t,"DOMException")){const i=t;if("stack"in t)o=Er(e,t);else{const a=i.name||(Bs(i)?"DOMError":"DOMException"),c=i.message?`${a}: ${i.message}`:a;o=ts(e,c,n,r),Fr(o,c)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}return ms(t)?Er(e,t):Zt(t)||tr(t)?(o=Fu(e,t,n,s),Tt(o,{synthetic:!0}),o):(o=ts(e,t,n,r),Fr(o,`${t}`),Tt(o,{synthetic:!0}),o)}function ts(e,t,n,r){const s={};if(r&&n){const o=xs(e,n);o.length&&(s.exception={values:[{value:t,stacktrace:{frames:o}}]}),Tt(s,{synthetic:!0})}if(hs(t)){const{__sentry_template_string__:o,__sentry_template_values__:i}=t;return s.logentry={message:o,params:i},s}return s.message=t,s}function zu(e,{isUnhandledRejection:t}){const n=function(s,o=40){const i=Object.keys(Di(s));i.sort();const a=i[0];if(!a)return"[object has no keys]";if(a.length>=o)return Un(a,o);for(let c=i.length;c>0;c--){const u=i.slice(0,c).join(", ");if(!(u.length>o))return c===i.length?u:Un(u,o)}return""}(e),r=t?"promise rejection":"exception";return Pi(e)?`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``:tr(e)?`Event \`${function(s){try{const o=Object.getPrototypeOf(s);return o?o.constructor.name:void 0}catch{}}(e)}\` (type=${e.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class Wu extends gu{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t};ua(n,"browser",["browser"],j.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:s,_experiments:o}=r._options,i=o==null?void 0:o.enableLogs;n.sendClientReports&&j.document&&j.document.addEventListener("visibilitychange",()=>{j.document.visibilityState==="hidden"&&(this._flushOutcomes(),i&&wr(r))}),i&&(r.on("flush",()=>{wr(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{wr(r)},5e3)})),s&&(r.on("postprocessEvent",Eu),r.on("beforeSendSession",xu))}eventFromException(t,n){return function(r,s,o,i){const a=es(r,s,(o==null?void 0:o.syntheticException)||void 0,i);return Tt(a),a.level="error",o!=null&&o.event_id&&(a.event_id=o.event_id),pt(a)}(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",r){return function(s,o,i="info",a,c){const u=ts(s,o,(a==null?void 0:a.syntheticException)||void 0,c);return u.level=i,a!=null&&a.event_id&&(u.event_id=a.event_id),pt(u)}(this._options.stackParser,t,n,r,this._options.attachStacktrace)}_prepareEvent(t,n,r,s){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,r,s)}}const Ts=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,At=(e,t,n,r)=>{let s,o;return i=>{t.value>=0&&(i||r)&&(o=t.value-(s||0),(o||s===void 0)&&(s=t.value,t.delta=o,t.rating=((a,c)=>a>c[1]?"poor":a>c[0]?"needs-improvement":"good")(t.value,n),e(t)))}},N=q,sn=(e=!0)=>{var n,r;const t=(r=(n=N.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!e||t&&t.responseStart>0&&t.responseStart<performance.now())return t},vn=()=>{const e=sn();return(e==null?void 0:e.activationStart)||0},Ot=(e,t)=>{var s,o;const n=sn();let r="navigate";return n&&((s=N.document)!=null&&s.prerendering||vn()>0?r="prerender":(o=N.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:t===void 0?-1:t,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},mt=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{t(s.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch{}},Lt=e=>{const t=n=>{var r;n.type!=="pagehide"&&((r=N.document)==null?void 0:r.visibilityState)!=="hidden"||e(n)};N.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},ir=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let Gt=-1;const Jn=e=>{N.document.visibilityState==="hidden"&&Gt>-1&&(Gt=e.type==="visibilitychange"?e.timeStamp:0,Ju())},Ju=()=>{removeEventListener("visibilitychange",Jn,!0),removeEventListener("prerenderingchange",Jn,!0)},ar=()=>(N.document&&Gt<0&&(Gt=N.document.visibilityState!=="hidden"||N.document.prerendering?1/0:0,addEventListener("visibilitychange",Jn,!0),addEventListener("prerenderingchange",Jn,!0)),{get firstHiddenTime(){return Gt}}),_n=e=>{var t;(t=N.document)!=null&&t.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},Gu=[1800,3e3],Ku=[.1,.25],Vu=(e,t={})=>{((n,r={})=>{_n(()=>{const s=ar(),o=Ot("FCP");let i;const a=mt("paint",c=>{c.forEach(u=>{u.name==="first-contentful-paint"&&(a.disconnect(),u.startTime<s.firstHiddenTime&&(o.value=Math.max(u.startTime-vn(),0),o.entries.push(u),i(!0)))})});a&&(i=At(n,o,Gu,r.reportAllChanges))})})(ir(()=>{const n=Ot("CLS",0);let r,s=0,o=[];const i=c=>{c.forEach(u=>{if(!u.hadRecentInput){const l=o[0],p=o[o.length-1];s&&l&&p&&u.startTime-p.startTime<1e3&&u.startTime-l.startTime<5e3?(s+=u.value,o.push(u)):(s=u.value,o=[u])}}),s>n.value&&(n.value=s,n.entries=o,r())},a=mt("layout-shift",i);a&&(r=At(e,n,Ku,t.reportAllChanges),Lt(()=>{i(a.takeRecords()),r(!0)}),setTimeout(r,0))}))},Yu=[100,300],Xu=(e,t={})=>{_n(()=>{const n=ar(),r=Ot("FID");let s;const o=c=>{c.startTime<n.firstHiddenTime&&(r.value=c.processingStart-c.startTime,r.entries.push(c),s(!0))},i=c=>{c.forEach(o)},a=mt("first-input",i);s=At(e,r,Yu,t.reportAllChanges),a&&Lt(ir(()=>{i(a.takeRecords()),a.disconnect()}))})};let va=0,xr=1/0,Pn=0;const Zu=e=>{e.forEach(t=>{t.interactionId&&(xr=Math.min(xr,t.interactionId),Pn=Math.max(Pn,t.interactionId),va=Pn?(Pn-xr)/7+1:0)})};let ns;const Qu=()=>{"interactionCount"in performance||ns||(ns=mt("event",Zu,{type:"event",buffered:!0,durationThreshold:0}))},Ae=[],Tr=new Map,el=()=>(ns?va:performance.interactionCount||0)-0,tl=[],nl=e=>{var r;if(tl.forEach(s=>s(e)),!e.interactionId&&e.entryType!=="first-input")return;const t=Ae[Ae.length-1],n=Tr.get(e.interactionId);if(n||Ae.length<10||t&&e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===((r=n.entries[0])==null?void 0:r.startTime)&&n.entries.push(e);else{const s={id:e.interactionId,latency:e.duration,entries:[e]};Tr.set(s.id,s),Ae.push(s)}Ae.sort((s,o)=>o.latency-s.latency),Ae.length>10&&Ae.splice(10).forEach(s=>Tr.delete(s.id))}},_a=e=>{var r;const t=N.requestIdleCallback||N.setTimeout;let n=-1;return e=ir(e),((r=N.document)==null?void 0:r.visibilityState)==="hidden"?e():(n=t(e),Lt(e)),n},rl=[200,500],sl=(e,t={})=>{"PerformanceEventTiming"in N&&"interactionId"in PerformanceEventTiming.prototype&&_n(()=>{Qu();const n=Ot("INP");let r;const s=i=>{_a(()=>{i.forEach(nl);const a=(()=>{const c=Math.min(Ae.length-1,Math.floor(el()/50));return Ae[c]})();a&&a.latency!==n.value&&(n.value=a.latency,n.entries=a.entries,r())})},o=mt("event",s,{durationThreshold:t.durationThreshold!=null?t.durationThreshold:40});r=At(e,n,rl,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),Lt(()=>{s(o.takeRecords()),r(!0)}))})},ol=[2500,4e3],Uo={},il=(e,t={})=>{_n(()=>{const n=ar(),r=Ot("LCP");let s;const o=a=>{t.reportAllChanges||(a=a.slice(-1)),a.forEach(c=>{c.startTime<n.firstHiddenTime&&(r.value=Math.max(c.startTime-vn(),0),r.entries=[c],s())})},i=mt("largest-contentful-paint",o);if(i){s=At(e,r,ol,t.reportAllChanges);const a=ir(()=>{Uo[r.id]||(o(i.takeRecords()),i.disconnect(),Uo[r.id]=!0,s(!0))});["keydown","click"].forEach(c=>{N.document&&addEventListener(c,()=>_a(a),{once:!0,capture:!0})}),Lt(a)}})},al=[800,1800],rs=e=>{var t,n;(t=N.document)!=null&&t.prerendering?_n(()=>rs(e)):((n=N.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>rs(e),!0):setTimeout(e,0)},cl=(e,t={})=>{const n=Ot("TTFB"),r=At(e,n,al,t.reportAllChanges);rs(()=>{const s=sn();s&&(n.value=Math.max(s.responseStart-vn(),0),n.entries=[s],r(!0))})},Kt={},Gn={};let ya,ba,Sa,wa,Ea;function xa(e,t=!1){return Vt("cls",e,ul,ya,t)}function zt(e,t){return Ta(e,t),Gn[e]||(function(n){const r={};n==="event"&&(r.durationThreshold=0),mt(n,s=>{Mt(n,{entries:s})},r)}(e),Gn[e]=!0),ka(e,t)}function Mt(e,t){const n=Kt[e];if(n!=null&&n.length)for(const r of n)try{r(t)}catch(s){Ts&&x.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${Me(r)}
Error:`,s)}}function ul(){return Vu(e=>{Mt("cls",{metric:e}),ya=e},{reportAllChanges:!0})}function ll(){return Xu(e=>{Mt("fid",{metric:e}),ba=e})}function dl(){return il(e=>{Mt("lcp",{metric:e}),Sa=e},{reportAllChanges:!0})}function fl(){return cl(e=>{Mt("ttfb",{metric:e}),wa=e})}function pl(){return sl(e=>{Mt("inp",{metric:e}),Ea=e})}function Vt(e,t,n,r,s=!1){let o;return Ta(e,t),Gn[e]||(o=n(),Gn[e]=!0),r&&t({metric:r}),ka(e,t,s?o:void 0)}function Ta(e,t){Kt[e]=Kt[e]||[],Kt[e].push(t)}function ka(e,t,n){return()=>{n&&n();const r=Kt[e];if(!r)return;const s=r.indexOf(t);s!==-1&&r.splice(s,1)}}function kr(e){return typeof e=="number"&&isFinite(e)}function Ce(e,t,n,{...r}){const s=B(e).start_timestamp;return s&&s>t&&typeof e.updateStartTime=="function"&&e.updateStartTime(t),ta(e,()=>{const o=ft({startTime:t,...r});return o&&o.end(n),o})}function $a(e){var m;const t=H();if(!t)return;const{name:n,transaction:r,attributes:s,startTime:o}=e,{release:i,environment:a,sendDefaultPii:c}=t.getOptions(),u=t.getIntegrationByName("Replay"),l=u==null?void 0:u.getReplayId(),p=W(),d=p.getUser(),f=d!==void 0?d.email||d.id||d.ip_address:void 0;let h;try{h=p.getScopeData().contexts.profile.profile_id}catch{}return ft({name:n,attributes:{release:i,environment:a,user:f||void 0,profile_id:h||void 0,replay_id:l||void 0,transaction:r,"user_agent.original":(m=N.navigator)==null?void 0:m.userAgent,"client.address":c?"{{auto}}":void 0,...s},startTime:o,experimental:{standalone:!0}})}function ks(){return N.addEventListener&&N.performance}function G(e){return e/1e3}function Ia(e){let t="unknown",n="unknown",r="";for(const s of e){if(s==="/"){[t,n]=e.split("/");break}if(!isNaN(Number(s))){t=r==="h"?"http":r,n=e.split(r)[1];break}r+=s}return r===e&&(t=r),{name:t,version:n}}function ml(){let e,t,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function s(){r||(r=!0,t&&function(i,a,c){var h;Ts&&x.log(`Sending CLS span (${i})`);const u=G((he()||0)+((a==null?void 0:a.startTime)||0)),l=W().getScopeData().transactionName,p=a?lt((h=a.sources[0])==null?void 0:h.node):"Layout shift",d={[X]:"auto.http.browser.cls",[dt]:"ui.webvital.cls",[en]:(a==null?void 0:a.duration)||0,"sentry.pageload.span_id":c},f=$a({name:p,transaction:l,attributes:d,startTime:u});f&&(f.addEvent("cls",{[rr]:"",[sr]:i}),f.end(u))}(n,e,t),o())}const o=xa(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(n=i.value,e=a)},!0);Lt(()=>{s()}),setTimeout(()=>{const i=H();if(!i)return;const a=i.on("startNavigationSpan",()=>{s(),a==null||a()}),c=ce();if(c){const u=te(c);B(u).op==="pageload"&&(t=u.spanContext().spanId)}},0)}const hl=2147483647;let se,bt,Ho=0,Z={};function gl({recordClsStandaloneSpans:e}){const t=ks();if(t&&he()){t.mark&&N.performance.mark("sentry-tracing-init");const n=Vt("fid",({metric:i})=>{const a=i.entries[i.entries.length-1];if(!a)return;const c=G(he()),u=G(a.startTime);Z.fid={value:i.value,unit:"millisecond"},Z["mark.fid"]={value:c+u,unit:"second"}},ll,ba),r=function(i,a=!1){return Vt("lcp",i,dl,Sa,a)}(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(Z.lcp={value:i.value,unit:"millisecond"},se=a)},!0),s=function(i){return Vt("ttfb",i,fl,wa)}(({metric:i})=>{i.entries[i.entries.length-1]&&(Z.ttfb={value:i.value,unit:"millisecond"})}),o=e?ml():xa(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(Z.cls={value:i.value,unit:""},bt=a)},!0);return()=>{n(),r(),s(),o==null||o()}}return()=>{}}function vl(e,t){const n=ks(),r=he();if(!(n!=null&&n.getEntries)||!r)return;const s=G(r),o=n.getEntries(),{op:i,start_timestamp:a}=B(e);if(o.slice(Ho).forEach(c=>{const u=G(c.startTime),l=G(Math.max(0,c.duration));if(!(i==="navigation"&&a&&s+u<a))switch(c.entryType){case"navigation":(function(p,d,f){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(h=>{An(p,d,h,f)}),An(p,d,"secureConnection",f,"TLS/SSL"),An(p,d,"fetch",f,"cache"),An(p,d,"domainLookup",f,"DNS"),function(h,m,g){const v=g+G(m.requestStart),_=g+G(m.responseEnd),S=g+G(m.responseStart);m.responseEnd&&(Ce(h,v,_,{op:"browser.request",name:m.name,attributes:{[X]:"auto.ui.browser.metrics"}}),Ce(h,S,_,{op:"browser.response",name:m.name,attributes:{[X]:"auto.ui.browser.metrics"}}))}(p,d,f)})(e,c,s);break;case"mark":case"paint":case"measure":{(function(f,h,m,g,v){const _=sn(!1),S=G(_?_.requestStart:0),y=v+Math.max(m,S),b=v+m,T=b+g,w={[X]:"auto.resource.browser.metrics"};if(y!==b&&(w["sentry.browser.measure_happened_before_request"]=!0,w["sentry.browser.measure_start_time"]=y),h.detail)if(typeof h.detail=="object")for(const[L,E]of Object.entries(h.detail))if(E&&Xt(E))w[`sentry.browser.measure.detail.${L}`]=E;else try{w[`sentry.browser.measure.detail.${L}`]=JSON.stringify(E)}catch{}else if(Xt(h.detail))w["sentry.browser.measure.detail"]=h.detail;else try{w["sentry.browser.measure.detail"]=JSON.stringify(h.detail)}catch{}y<=T&&Ce(f,y,T,{name:h.name,op:h.entryType,attributes:w})})(e,c,u,l,s);const p=ar(),d=c.startTime<p.firstHiddenTime;c.name==="first-paint"&&d&&(Z.fp={value:c.startTime,unit:"millisecond"}),c.name==="first-contentful-paint"&&d&&(Z.fcp={value:c.startTime,unit:"millisecond"});break}case"resource":(function(p,d,f,h,m,g){if(d.initiatorType==="xmlhttprequest"||d.initiatorType==="fetch")return;const v=xt(f),_={[X]:"auto.resource.browser.metrics"};$r(_,d,"transferSize","http.response_transfer_size"),$r(_,d,"encodedBodySize","http.response_content_length"),$r(_,d,"decodedBodySize","http.decoded_response_content_length");const S=d.deliveryType;S!=null&&(_["http.response_delivery_type"]=S);const y=d.renderBlockingStatus;y&&(_["resource.render_blocking_status"]=y),v.protocol&&(_["url.scheme"]=v.protocol.split(":").pop()),v.host&&(_["server.address"]=v.host),_["url.same_origin"]=f.includes(N.location.origin);const{name:b,version:T}=Ia(d.nextHopProtocol);_["network.protocol.name"]=b,_["network.protocol.version"]=T;const w=g+h,L=w+m;Ce(p,w,L,{name:f.replace(N.location.origin,""),op:d.initiatorType?`resource.${d.initiatorType}`:"resource.other",attributes:_})})(e,c,c.name,u,l,s)}}),Ho=Math.max(o.length-1,0),function(c){const u=N.navigator;if(!u)return;const l=u.connection;l&&(l.effectiveType&&c.setAttribute("effectiveConnectionType",l.effectiveType),l.type&&c.setAttribute("connectionType",l.type),kr(l.rtt)&&(Z["connection.rtt"]={value:l.rtt,unit:"millisecond"})),kr(u.deviceMemory)&&c.setAttribute("deviceMemory",`${u.deviceMemory} GB`),kr(u.hardwareConcurrency)&&c.setAttribute("hardwareConcurrency",String(u.hardwareConcurrency))}(e),i==="pageload"){(function(u){const l=sn(!1);if(!l)return;const{responseStart:p,requestStart:d}=l;d<=p&&(u["ttfb.requestTime"]={value:p-d,unit:"millisecond"})})(Z);const c=Z["mark.fid"];c&&Z.fid&&(Ce(e,c.value,c.value+G(Z.fid.value),{name:"first input delay",op:"ui.action",attributes:{[X]:"auto.ui.browser.metrics"}}),delete Z["mark.fid"]),"fcp"in Z&&t.recordClsOnPageloadSpan||delete Z.cls,Object.entries(Z).forEach(([u,l])=>{(function(p,d,f,h=ce()){const m=h&&te(h);m&&(O&&x.log(`[Measurement] Setting measurement on root span: ${p} = ${d} ${f}`),m.addEvent(p,{[sr]:d,[rr]:f}))})(u,l.value,l.unit)}),e.setAttribute("performance.timeOrigin",s),e.setAttribute("performance.activationStart",vn()),function(u){se&&(se.element&&u.setAttribute("lcp.element",lt(se.element)),se.id&&u.setAttribute("lcp.id",se.id),se.url&&u.setAttribute("lcp.url",se.url.trim().slice(0,200)),se.loadTime!=null&&u.setAttribute("lcp.loadTime",se.loadTime),se.renderTime!=null&&u.setAttribute("lcp.renderTime",se.renderTime),u.setAttribute("lcp.size",se.size)),bt!=null&&bt.sources&&bt.sources.forEach((l,p)=>u.setAttribute(`cls.source.${p+1}`,lt(l.node)))}(e)}se=void 0,bt=void 0,Z={}}function An(e,t,n,r,s=n){const o=function(c){return c==="secureConnection"?"connectEnd":c==="fetch"?"domainLookupStart":`${c}End`}(n),i=t[o],a=t[`${n}Start`];a&&i&&Ce(e,r+G(a),r+G(i),{op:`browser.${s}`,name:t.name,attributes:{[X]:"auto.ui.browser.metrics",...n==="redirect"&&t.redirectCount!=null?{"http.redirect_count":t.redirectCount}:{}}})}function $r(e,t,n,r){const s=t[n];s!=null&&s<hl&&(e[r]=s)}const _l=1e3;let Bo,Ir,Pr,On;function yl(){if(!N.document)return;const e=ve.bind(null,"dom"),t=zo(e,!0);N.document.addEventListener("click",t,!1),N.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{var o,i;const r=N,s=(o=r[n])==null?void 0:o.prototype;(i=s==null?void 0:s.hasOwnProperty)!=null&&i.call(s,"addEventListener")&&(ue(s,"addEventListener",function(a){return function(c,u,l){if(c==="click"||c=="keypress")try{const p=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},d=p[c]=p[c]||{refCount:0};if(!d.handler){const f=zo(e);d.handler=f,a.call(this,c,f,l)}d.refCount++}catch{}return a.call(this,c,u,l)}}),ue(s,"removeEventListener",function(a){return function(c,u,l){if(c==="click"||c=="keypress")try{const p=this.__sentry_instrumentation_handlers__||{},d=p[c];d&&(d.refCount--,d.refCount<=0&&(a.call(this,c,d.handler,l),d.handler=void 0,delete p[c]),Object.keys(p).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return a.call(this,c,u,l)}}))})}function zo(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(o){try{return o.target}catch{return null}}(n);if(function(o,i){return o==="keypress"&&(!(i!=null&&i.tagName)||i.tagName!=="INPUT"&&i.tagName!=="TEXTAREA"&&!i.isContentEditable)}(n.type,r))return;de(n,"_sentryCaptured",!0),r&&!r._sentryId&&de(r,"_sentryId",ge());const s=n.type==="keypress"?"input":n.type;(function(o){if(o.type!==Ir)return!1;try{if(!o.target||o.target._sentryId!==Pr)return!1}catch{}return!0})(n)||(e({event:n,name:s,global:t}),Ir=n.type,Pr=r?r._sentryId:void 0),clearTimeout(Bo),Bo=N.setTimeout(()=>{Pr=void 0,Ir=void 0},_l)}}function $s(e){const t="history";ze(t,e),We(t,bl)}function bl(){function e(t){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=On,o=function(i){try{return new URL(i,N.location.origin).toString()}catch{return i}}(String(r));if(On=o,s===o)return t.apply(this,n);ve("history",{from:s,to:o})}return t.apply(this,n)}}N.addEventListener("popstate",()=>{const t=N.location.href,n=On;On=t,n!==t&&ve("history",{from:n,to:t})}),"history"in rn&&rn.history&&(ue(N.history,"pushState",e),ue(N.history,"replaceState",e))}const Mn={};function Wo(e){Mn[e]=void 0}const wt="__sentry_xhr_v3__";function Pa(e){ze("xhr",e),We("xhr",Sl)}function Sl(){if(!N.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,r){const s=new Error,o=1e3*ne(),i=Re(r[0])?r[0].toUpperCase():void 0,a=function(u){if(Re(u))return u;try{return u.toString()}catch{}}(r[1]);if(!i||!a)return t.apply(n,r);n[wt]={method:i,url:a,request_headers:{}},i==="POST"&&a.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const c=()=>{const u=n[wt];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}ve("xhr",{endTimestamp:1e3*ne(),startTimestamp:o,xhr:n,virtualError:s})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,l,p)=>(c(),u.apply(l,p))}):n.addEventListener("readystatechange",c),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,l,p){const[d,f]=p,h=l[wt];return h&&Re(d)&&Re(f)&&(h.request_headers[d.toLowerCase()]=f),u.apply(l,p)}}),t.apply(n,r)}}),e.send=new Proxy(e.send,{apply(t,n,r){const s=n[wt];return s?(r[0]!==void 0&&(s.body=r[0]),ve("xhr",{startTimestamp:1e3*ne(),xhr:n}),t.apply(n,r)):t.apply(n,r)}})}const Ar=[],jn=new Map;function wl(){if(ks()&&he()){const e=Vt("inp",({metric:t})=>{if(t.value==null)return;const n=t.entries.find(d=>d.duration===t.value&&Jo[d.name]);if(!n)return;const{interactionId:r}=n,s=Jo[n.name],o=G(he()+n.startTime),i=G(t.value),a=ce(),c=a?te(a):void 0,u=(r!=null?jn.get(r):void 0)||c,l=u?B(u).description:W().getScopeData().transactionName,p=$a({name:lt(n.target),transaction:l,attributes:{[X]:"auto.http.browser.inp",[dt]:`ui.interaction.${s}`,[en]:n.duration},startTime:o});p&&(p.addEvent("inp",{[rr]:"millisecond",[sr]:t.value}),p.end(o+i))},pl,Ea);return()=>{e()}}return()=>{}}const Jo={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function El(e,t=function(n){const r=Mn[n];if(r)return r;let s=N[n];if(Yr(s))return Mn[n]=s.bind(N);const o=N.document;if(o&&typeof o.createElement=="function")try{const i=o.createElement("iframe");i.hidden=!0,o.head.appendChild(i);const a=i.contentWindow;a!=null&&a[n]&&(s=a[n]),o.head.removeChild(i)}catch(i){Ts&&x.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,i)}return s&&(Mn[n]=s.bind(N))}("fetch")){let n=0,r=0;return wu(e,function(s){const o=s.body.length;n+=o,r++;const i={body:s.body,method:"POST",referrerPolicy:"strict-origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return Wo("fetch"),Wn("No fetch implementation available");try{return t(e.url,i).then(a=>(n-=o,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return Wo("fetch"),n-=o,r--,Wn(a)}})}function Or(e,t,n,r){const s={filename:e,function:t==="<anonymous>"?at:t,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const xl=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Tl=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,kl=/\((\S*)(?::(\d+))(?::(\d+))\)/,$l=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Il=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Pl=Ji([30,e=>{const t=xl.exec(e);if(t){const[,r,s,o]=t;return Or(r,at,+s,+o)}const n=Tl.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=kl.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[r,s]=Go(n[1]||at,n[2]);return Or(s,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,e=>{const t=$l.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const s=Il.exec(t[3]);s&&(t[1]=t[1]||"eval",t[3]=s[1],t[4]=s[2],t[5]="")}let n=t[3],r=t[1]||at;return[r,n]=Go(r,n),Or(n,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),Go=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,r=e.indexOf("safari-web-extension")!==-1;return n||r?[e.indexOf("@")!==-1?e.split("@")[0]:at,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},me=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Ko=1024,Al=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(n){var r;t.console&&function(s){const o="console";ze(o,s),We(o,Ou)}(function(s){return function(o){if(H()!==s)return;const i={category:"console",data:{arguments:o.args,logger:"console"},level:Cu(o.level),message:zs(o.args," ")};if(o.level==="assert"){if(o.args[0]!==!1)return;i.message=`Assertion failed: ${zs(o.args.slice(1)," ")||"console.assert"}`,i.data.arguments=o.args.slice(1)}Qe(i,{input:o.args,level:o.level})}}(n)),t.dom&&(r=function(s,o){return function(i){if(H()!==s)return;let a,c,u=typeof o=="object"?o.serializeAttribute:void 0,l=typeof o=="object"&&typeof o.maxStringLength=="number"?o.maxStringLength:void 0;l&&l>Ko&&(me&&x.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${l} was configured. Sentry will use 1024 instead.`),l=Ko),typeof u=="string"&&(u=[u]);try{const d=i.event,f=function(h){return!!h&&!!h.target}(d)?d.target:d;a=lt(f,{keyAttrs:u,maxStringLength:l}),c=Ci(f)}catch{a="<unknown>"}if(a.length===0)return;const p={category:`ui.${i.name}`,message:a};c&&(p.data={"ui.component_name":c}),Qe(p,{event:i.event,name:i.name,global:i.global})}}(n,t.dom),ze("dom",r),We("dom",yl)),t.xhr&&Pa(function(s){return function(o){if(H()!==s)return;const{startTimestamp:i,endTimestamp:a}=o,c=o.xhr[wt];if(!i||!a||!c)return;const{method:u,url:l,status_code:p,body:d}=c,f={method:u,url:l,status_code:p},h={xhr:o.xhr,input:d,startTimestamp:i,endTimestamp:a},m={category:"xhr",data:f,type:"http",level:jo(p)};s.emit("beforeOutgoingRequestBreadcrumb",m,h),Qe(m,h)}}(n)),t.fetch&&ma(function(s){return function(o){if(H()!==s)return;const{startTimestamp:i,endTimestamp:a}=o;if(a&&(!o.fetchData.url.match(/sentry_key/)||o.fetchData.method!=="POST"))if(o.fetchData.method,o.fetchData.url,o.error){const c=o.fetchData,u={data:o.error,input:o.args,startTimestamp:i,endTimestamp:a},l={category:"fetch",data:c,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",l,u),Qe(l,u)}else{const c=o.response,u={...o.fetchData,status_code:c==null?void 0:c.status};o.fetchData.request_body_size,o.fetchData.response_body_size;const l={input:o.args,response:c,startTimestamp:i,endTimestamp:a},p={category:"fetch",data:u,type:"http",level:jo(u.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",p,l),Qe(p,l)}}}(n)),t.history&&$s(function(s){return function(o){if(H()!==s)return;let i=o.from,a=o.to;const c=xt(j.location.href);let u=i?xt(i):void 0;const l=xt(a);u!=null&&u.path||(u=c),c.protocol===l.protocol&&c.host===l.host&&(a=l.relative),c.protocol===u.protocol&&c.host===u.host&&(i=u.relative),Qe({category:"navigation",data:{from:i,to:a}})}}(n)),t.sentry&&n.on("beforeSendEvent",function(s){return function(o){H()===s&&Qe({category:"sentry."+(o.type==="transaction"?"transaction":"event"),event_id:o.event_id,level:o.level,message:et(o)},{event:o})}}(n))}}},Ol=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Cl=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&ue(j,"setTimeout",Vo),t.setInterval&&ue(j,"setInterval",Vo),t.requestAnimationFrame&&ue(j,"requestAnimationFrame",Rl),t.XMLHttpRequest&&"XMLHttpRequest"in j&&ue(XMLHttpRequest.prototype,"send",Dl);const n=t.eventTarget;n&&(Array.isArray(n)?n:Ol).forEach(Nl)}}};function Vo(e){return function(...t){const n=t[0];return t[0]=Pt(n,{mechanism:{data:{function:Me(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function Rl(e){return function(t){return e.apply(this,[Pt(t,{mechanism:{data:{function:"requestAnimationFrame",handler:Me(e)},handled:!1,type:"instrument"}})])}}function Dl(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&ue(n,r,function(s){const o={mechanism:{data:{function:r,handler:Me(s)},handled:!1,type:"instrument"}},i=vs(s);return i&&(o.mechanism.data.handler=Me(i)),Pt(s,o)})}),e.apply(this,t)}}function Nl(e){var r,s;const t=j,n=(r=t[e])==null?void 0:r.prototype;(s=n==null?void 0:n.hasOwnProperty)!=null&&s.call(n,"addEventListener")&&(ue(n,"addEventListener",function(o){return function(i,a,c){try{typeof a.handleEvent=="function"&&(a.handleEvent=Pt(a.handleEvent,{mechanism:{data:{function:"handleEvent",handler:Me(a),target:e},handled:!1,type:"instrument"}}))}catch{}return o.apply(this,[i,Pt(a,{mechanism:{data:{function:"addEventListener",handler:Me(a),target:e},handled:!1,type:"instrument"}}),c])}}),ue(n,"removeEventListener",function(o){return function(i,a,c){try{const u=a.__sentry_wrapped__;u&&o.call(this,i,u,c)}catch{}return o.call(this,i,a,c)}}))}const Ll=()=>({name:"BrowserSession",setupOnce(){j.document!==void 0?(bo({ignoreDuration:!0}),So(),$s(({from:e,to:t})=>{e!==void 0&&e!==t&&(bo({ignoreDuration:!0}),So())})):me&&x.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),Ml=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(function(r){Gi(s=>{const{stackParser:o,attachStacktrace:i}=Xo();if(H()!==r||Fo())return;const{msg:a,url:c,line:u,column:l,error:p}=s,d=function(f,h,m,g){const v=f.exception=f.exception||{},_=v.values=v.values||[],S=_[0]=_[0]||{},y=S.stacktrace=S.stacktrace||{},b=y.frames=y.frames||[],T=g,w=m,L=Re(h)&&h.length>0?h:pn();return b.length===0&&b.push({colno:T,filename:L,function:at,in_app:!0,lineno:w}),f}(es(o,p||a,void 0,i,!1),c,u,l);d.level="error",yo(d,{originalException:p,mechanism:{handled:!1,type:"onerror"}})})}(n),Yo("onerror")),t.onunhandledrejection&&(function(r){Ki(s=>{const{stackParser:o,attachStacktrace:i}=Xo();if(H()!==r||Fo())return;const a=function(u){if(Xt(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(s),c=Xt(a)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(a)}`}]}}:es(o,a,void 0,i,!0);c.level="error",yo(c,{originalException:a,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),Yo("onunhandledrejection"))}}};function Yo(e){me&&x.log(`Global Handler attached: ${e}`)}function Xo(){const e=H();return(e==null?void 0:e.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const jl=()=>({name:"HttpContext",preprocessEvent(e){var r;if(!j.navigator&&!j.location&&!j.document)return;const t=Qr(),n={...t.headers,...(r=e.request)==null?void 0:r.headers};e.request={...t,...e.request,headers:n}}}),ql=(e={})=>{const t=e.limit||5,n=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,s,o){Au(Es,o.getOptions().stackParser,n,t,r,s)}}};function Fl(e){const t={};for(const n of Object.getOwnPropertyNames(e)){const r=n;e[r]!==void 0&&(t[r]=e[r])}return t}function Ul(e={}){const t=function(s={}){var o;return{defaultIntegrations:[Pu(),ku(),Cl(),Al(),Ml(),ql(),Ru(),jl(),Ll()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(o=j.SENTRY_RELEASE)==null?void 0:o.id,sendClientReports:!0,...Fl(s)}}(e);if(!t.skipBrowserExtensionCheck&&function(){var l;const s=j.window!==void 0&&j;if(!s)return!1;const o=s[s.chrome?"chrome":"browser"],i=(l=o==null?void 0:o.runtime)==null?void 0:l.id,a=pn()||"",c=!!i&&j===j.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(p=>a.startsWith(`${p}//`)),u=s.nw!==void 0;return!!i&&!c&&!u}())return void(me&&vt(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));me&&!pa()&&x.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...t,stackParser:(r=t.stackParser||Pl,Array.isArray(r)?Ji(...r):r),integrations:hu(t),transport:t.transport||El};var r;return vu(Wu,n)}const Zo=new WeakMap,Cr=new Map,Aa={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function Hl(e,t){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:o,enableHTTPTimings:i,tracePropagationTargets:a,onRequestSpanStart:c}={...Aa,...t},u=typeof o=="function"?o:d=>!0,l=d=>function(f,h){const m=pn();if(m){let g,v;try{g=new URL(f,m),v=new URL(m).origin}catch{return!1}const _=g.origin===v;return h?nt(g.toString(),h)||_&&nt(g.pathname,h):_}{const g=!!f.match(/^\/(?!\/)/);return h?nt(f,h):g}}(d,a),p={};n&&(e.addEventProcessor(d=>(d.type==="transaction"&&d.spans&&d.spans.forEach(f=>{if(f.op==="http.client"){const h=Cr.get(f.span_id);h&&(f.timestamp=h/1e3,Cr.delete(f.span_id))}}),d)),s&&function(d){const f="fetch-body-resolved";ze(f,d),We(f,()=>ha(ju))}(d=>{if(d.response){const f=Zo.get(d.response);f&&d.endTimestamp&&Cr.set(f,d.endTimestamp)}}),ma(d=>{const f=Lu(d,u,l,p);if(d.response&&d.fetchData.__span&&Zo.set(d.response,d.fetchData.__span),f){const h=ei(d.fetchData.url),m=h?xt(h).host:void 0;f.setAttributes({"http.url":h,"server.address":m}),i&&Qo(f),c==null||c(f,{headers:d.headers})}})),r&&Pa(d=>{var h;const f=function(m,g,v,_){const S=m.xhr,y=S==null?void 0:S[wt];if(!S||S.__sentry_own_request__||!y)return;const{url:b,method:T}=y,w=Je()&&g(b);if(m.endTimestamp&&w){const P=S.__sentry_xhr_span_id__;if(!P)return;const $=_[P];return void($&&y.status_code!==void 0&&(qi($,y.status_code),$.end(),delete _[P]))}const L=ei(b),E=xt(L||b),R=(A=b,A.split(/[?#]/,1)[0]),D=!!ce(),k=w&&D?ft({name:`${T} ${R}`,attributes:{url:b,type:"xhr","http.method":T,"http.url":L,"server.address":E==null?void 0:E.host,[X]:"auto.http.browser",[dt]:"http.client",...(E==null?void 0:E.search)&&{"http.query":E==null?void 0:E.search},...(E==null?void 0:E.hash)&&{"http.fragment":E==null?void 0:E.hash}}}):new ct;var A;S.__sentry_xhr_span_id__=k.spanContext().spanId,_[S.__sentry_xhr_span_id__]=k,v(b)&&function(P,$){const{"sentry-trace":C,baggage:U}=la({span:$});C&&function(re,J,be){var Hs;const ie=(Hs=re.__sentry_xhr_v3__)==null?void 0:Hs.request_headers;if(!(ie!=null&&ie["sentry-trace"]))try{if(re.setRequestHeader("sentry-trace",J),be){const mr=ie==null?void 0:ie.baggage;mr&&mr.split(",").some(Oc=>Oc.trim().startsWith("sentry-"))||re.setRequestHeader("baggage",be)}}catch{}}(P,C,U)}(S,Je()&&D?k:void 0);const I=H();return I&&I.emit("beforeOutgoingRequestSpan",k,m),k}(d,u,l,p);if(f){let m;i&&Qo(f);try{m=new Headers((h=d.xhr.__sentry_xhr_v3__)==null?void 0:h.request_headers)}catch{}c==null||c(f,{headers:m})}})}function Qo(e){const{url:t}=B(e).data;if(!t||typeof t!="string")return;const n=zt("resource",({entries:r})=>{r.forEach(s=>{(function(o){return o.entryType==="resource"&&"initiatorType"in o&&typeof o.nextHopProtocol=="string"&&(o.initiatorType==="fetch"||o.initiatorType==="xmlhttprequest")})(s)&&s.name.endsWith(t)&&(function(o){const{name:i,version:a}=Ia(o.nextHopProtocol),c=[];return c.push(["network.protocol.version",a],["network.protocol.name",i]),he()?[...c,["http.request.redirect_start",Se(o.redirectStart)],["http.request.fetch_start",Se(o.fetchStart)],["http.request.domain_lookup_start",Se(o.domainLookupStart)],["http.request.domain_lookup_end",Se(o.domainLookupEnd)],["http.request.connect_start",Se(o.connectStart)],["http.request.secure_connection_start",Se(o.secureConnectionStart)],["http.request.connection_end",Se(o.connectEnd)],["http.request.request_start",Se(o.requestStart)],["http.request.response_start",Se(o.responseStart)],["http.request.response_end",Se(o.responseEnd)]]:c}(s).forEach(o=>e.setAttribute(...o)),setTimeout(n))})})}function Se(e=0){return((he()||performance.timeOrigin)+e)/1e3}function ei(e){try{return new URL(e,j.location.origin).href}catch{return}}const Bl=3600,ti="sentry_previous_trace",zl="sentry.previous_trace";function Wl(e,{linkPreviousTrace:t,consistentTraceSampling:n}){const r=t==="session-storage";let s=r?function(){var i;try{const a=(i=j.sessionStorage)==null?void 0:i.getItem(ti);return JSON.parse(a)}catch{return}}():void 0;e.on("spanStart",i=>{if(te(i)!==i)return;const a=W().getPropagationContext();s=function(c,u,l){const p=B(u);function d(){var m,g;try{return Number((m=l.dsc)==null?void 0:m.sample_rate)??Number((g=p.data)==null?void 0:g[ys])}catch{return 0}}const f={spanContext:u.spanContext(),startTimestamp:p.start_timestamp,sampleRate:d(),sampleRand:l.sampleRand};if(!c)return f;const h=c.spanContext;return h.traceId===p.trace_id?c:(Date.now()/1e3-c.startTimestamp<=Bl&&(me&&x.info(`Adding previous_trace ${h} link to span ${{op:p.op,...u.spanContext()}}`),u.addLink({context:h,attributes:{[Hc]:"previous_trace"}}),u.setAttribute(zl,`${h.traceId}-${h.spanId}-${Rr(h)?1:0}`)),f)}(s,i,a),r&&function(c){try{j.sessionStorage.setItem(ti,JSON.stringify(c))}catch(u){me&&x.warn("Could not store previous trace in sessionStorage",u)}}(s)});let o=!0;n&&e.on("beforeSampling",i=>{if(!s)return;const a=W(),c=a.getPropagationContext();o&&c.parentSpanId?o=!1:(a.setPropagationContext({...c,dsc:{...c.dsc,sample_rate:String(s.sampleRate),sampled:String(Rr(s.spanContext))},sampleRand:s.sampleRand}),i.parentSampled=Rr(s.spanContext),i.parentSampleRate=s.sampleRate,i.spanAttributes={...i.spanAttributes,[Mi]:s.sampleRate})})}function Rr(e){return e.traceFlags===1}const Jl={...Ln,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...Aa};let ni=!1;const Gl=(e={})=>{ni&&vt(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),ni=!0;const t=j.document;ao||(ao=!0,Gi(Wr),Ki(Wr));const{enableInp:n,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:o,enableStandaloneClsSpans:i},beforeStartSpan:a,idleTimeout:c,finalTimeout:u,childSpanTimeout:l,markBackgroundSpan:p,traceFetch:d,traceXHR:f,trackFetchStreamPerformance:h,shouldCreateSpanForRequest:m,enableHTTPTimings:g,instrumentPageLoad:v,instrumentNavigation:_,linkPreviousTrace:S,consistentTraceSampling:y,onRequestSpanStart:b}={...Jl,...e},T=gl({recordClsStandaloneSpans:i||!1});n&&wl(),s&&q.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(E=>{const R=ce();if(R)for(const D of E.getEntries()){if(!D.scripts[0])continue;const k=G(he()+D.startTime),{start_timestamp:A,op:I}=B(R);if(I==="navigation"&&A&&k<A)continue;const P=G(D.duration),$={[X]:"auto.ui.browser.metrics"},C=D.scripts[0],{invoker:U,invokerType:re,sourceURL:J,sourceFunctionName:be,sourceCharPosition:ie}=C;$["browser.script.invoker"]=U,$["browser.script.invoker_type"]=re,J&&($["code.filepath"]=J),be&&($["code.function"]=be),ie!==-1&&($["browser.script.source_char_position"]=ie),Ce(R,k,k+P,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:$})}}).observe({type:"long-animation-frame",buffered:!0}):r&&zt("longtask",({entries:E})=>{const R=ce();if(!R)return;const{op:D,start_timestamp:k}=B(R);for(const A of E){const I=G(he()+A.startTime),P=G(A.duration);D==="navigation"&&k&&I<k||Ce(R,I,I+P,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[X]:"auto.ui.browser.metrics"}})}}),o&&zt("event",({entries:E})=>{const R=ce();if(R){for(const D of E)if(D.name==="click"){const k=G(he()+D.startTime),A=G(D.duration),I={name:lt(D.target),op:`ui.interaction.${D.name}`,startTime:k,attributes:{[X]:"auto.ui.browser.metrics"}},P=Ci(D.target);P&&(I.attributes["ui.component_name"]=P),Ce(R,k,k+A,I)}}});const w={name:void 0,source:void 0};function L(E,R){const D=R.op==="pageload",k=a?a(R):R,A=k.attributes||{};R.name!==k.name&&(A[Oe]="custom",k.attributes=A),w.name=k.name,w.source=A[Oe];const I=go(k,{idleTimeout:c,finalTimeout:u,childSpanTimeout:l,disableAutoFinish:D,beforeSpanEnd:$=>{T(),vl($,{recordClsOnPageloadSpan:!i}),si(E,void 0);const C=W(),U=C.getPropagationContext();C.setPropagationContext({...U,traceId:I.spanContext().traceId,sampled:ot(I),dsc:Ge($)})}});function P(){t&&["interactive","complete"].includes(t.readyState)&&E.emit("idleSpanEnableAutoFinish",I)}si(E,I),D&&t&&(t.addEventListener("readystatechange",()=>{P()}),P())}return{name:"BrowserTracing",afterAllSetup(E){let R=pn();function D(){const k=Cn(E);k&&!B(k).timestamp&&(me&&x.log(`[Tracing] Finishing current active span with op: ${B(k).op}`),k.setAttribute(Bn,"cancelled"),k.end())}if(E.on("startNavigationSpan",k=>{if(H()!==E)return;D(),Ye().setPropagationContext({traceId:Be(),sampleRand:Math.random()});const A=W();A.setPropagationContext({traceId:Be(),sampleRand:Math.random()}),A.setSDKProcessingMetadata({normalizedRequest:void 0}),L(E,{op:"navigation",...k})}),E.on("startPageLoadSpan",(k,A={})=>{if(H()!==E)return;D();const I=Gc(A.sentryTrace||ri("sentry-trace"),A.baggage||ri("baggage")),P=W();P.setPropagationContext(I),P.setSDKProcessingMetadata({normalizedRequest:Qr()}),L(E,{op:"pageload",...k})}),S!=="off"&&Wl(E,{linkPreviousTrace:S,consistentTraceSampling:y}),j.location){if(v){const k=he();(function(A,I,P){A.emit("startPageLoadSpan",I,P),W().setTransactionName(I.name),Cn(A)})(E,{name:j.location.pathname,startTime:k?k/1e3:void 0,attributes:{[Oe]:"url",[X]:"auto.pageload.browser"}})}_&&$s(({to:k,from:A})=>{if(A===void 0&&(R==null?void 0:R.indexOf(k))!==-1)return void(R=void 0);R=void 0;const I=fa(k);(function(P,$){P.emit("startNavigationSpan",$),W().setTransactionName($.name),Cn(P)})(E,{name:(I==null?void 0:I.pathname)||j.location.pathname,attributes:{[Oe]:"url",[X]:"auto.navigation.browser"}}),W().setSDKProcessingMetadata({normalizedRequest:{...Qr(),url:k}})})}p&&(j.document?j.document.addEventListener("visibilitychange",()=>{const k=ce();if(!k)return;const A=te(k);if(j.document.hidden&&A){const I="cancelled",{op:P,status:$}=B(A);me&&x.log(`[Tracing] Transaction: ${I} -> since tab moved to the background, op: ${P}`),$||A.setStatus({code:Y,message:I}),A.setAttribute("sentry.cancellation_reason","document.hidden"),A.end()}}):me&&x.warn("[Tracing] Could not set up background tab detection due to lack of global document")),o&&function(k,A,I,P,$){const C=j.document;let U;const re=()=>{const J="ui.action.click",be=Cn(k);if(be){const ie=B(be).op;if(["navigation","pageload"].includes(ie))return void(me&&x.warn(`[Tracing] Did not create ${J} span because a pageload or navigation span is in progress.`))}U&&(U.setAttribute(Bn,"interactionInterrupted"),U.end(),U=void 0),$.name?U=go({name:$.name,op:J,attributes:{[Oe]:$.source||"url"}},{idleTimeout:A,finalTimeout:I,childSpanTimeout:P}):me&&x.warn(`[Tracing] Did not create ${J} transaction because _latestRouteName is missing.`)};C&&addEventListener("click",re,{once:!1,capture:!0})}(E,c,u,l,w),n&&function(){const k=({entries:A})=>{const I=ce(),P=I&&te(I);A.forEach($=>{if(!function(U){return"duration"in U}($)||!P)return;const C=$.interactionId;if(C!=null&&!jn.has(C)){if(Ar.length>10){const U=Ar.shift();jn.delete(U)}Ar.push(C),jn.set(C,P)}})};zt("event",k),zt("first-input",k)}(),Hl(E,{traceFetch:d,traceXHR:f,trackFetchStreamPerformance:h,tracePropagationTargets:E.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:m,enableHTTPTimings:g,onRequestSpanStart:b})}}};function ri(e){const t=j.document,n=t==null?void 0:t.querySelector(`meta[name=${e}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}const Oa="_sentry_idleSpan";function Cn(e){return e[Oa]}function si(e,t){de(e,Oa,t)}const Kl=!1;var cr=Array.isArray,Vl=Array.prototype.indexOf,Yl=Array.from,Ca=Object.defineProperty,He=Object.getOwnPropertyDescriptor,Ra=Object.getOwnPropertyDescriptors,Xl=Object.prototype,Zl=Array.prototype,Is=Object.getPrototypeOf,oi=Object.isExtensible;function Ht(e){return typeof e=="function"}const De=()=>{};function nf(e){return typeof(e==null?void 0:e.then)=="function"}function Ql(e){return e()}function on(e){for(var t=0;t<e.length;t++)e[t]()}function rf(e,t,n=!1){return e===void 0?n?t():t:e}function sf(e,t){if(Array.isArray(e))return e;if(!(Symbol.iterator in e))return Array.from(e);const n=[];for(const r of e)if(n.push(r),n.length===t)break;return n}const _e=2,Ps=4,yn=8,As=16,qe=32,jt=64,Os=128,le=256,Kn=512,fe=1024,je=2048,Xe=4096,Ct=8192,Cs=16384,Da=32768,Rs=65536,ii=1<<17,ed=1<<18,Na=1<<19,ss=1<<20,Ds=1<<21,xe=Symbol("$state"),La=Symbol("legacy props"),td=Symbol(""),Ma=new class extends Error{constructor(){super(...arguments);hr(this,"name","StaleReactionError");hr(this,"message","The reaction that called `getAbortSignal()` was re-run or destroyed")}};function ja(e){return e===this.v}function qa(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function of(e,t){return e!==t}function Fa(e){return!qa(e,this.v)}let qt=!1,nd=!1;const af=1,cf=2,uf=4,lf=8,df=16,rd=1,sd=2,Ua=4,od=8,id=16,ff=1,pf=2,mf=4,oe=Symbol(),ad="http://www.w3.org/1999/xhtml",hf="http://www.w3.org/2000/svg",cd="@attach";function gf(){throw new Error("https://svelte.dev/e/invalid_default_snippet")}function bn(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let M=null;function ai(e){M=e}function vf(e){return za().get(e)}function _f(e,t){return za().set(e,t),t}function Ha(e,t=!1,n){var r=M={p:M,c:null,d:!1,e:null,m:!1,s:e,x:null,l:null};qt&&!t&&(M.l={s:null,u:null,r1:[],r2:lr(!1)}),Sn(()=>{r.d=!0})}function Ba(e){const t=M;if(t!==null){e!==void 0&&(t.x=e);const i=t.e;if(i!==null){var n=z,r=F;t.e=null;try{for(var s=0;s<i.length;s++){var o=i[s];Ve(o.effect),ke(o.reaction),tc(o.fn)}}finally{Ve(n),ke(r)}}M=t.p,t.m=!0}return e||{}}function ur(){return!qt||M!==null&&M.l===null}function za(e){return M===null&&bn(),M.c??(M.c=new Map(function(t){let n=t.p;for(;n!==null;){const r=n.c;if(r!==null)return r;n=n.p}return null}(M)||void 0))}function Et(e){if(typeof e!="object"||e===null||xe in e)return e;const t=Is(e);if(t!==Xl&&t!==Zl)return e;var n=new Map,r=cr(e),s=Fe(0),o=F,i=a=>{var c=F;ke(o);var u=a();return ke(c),u};return r&&n.set("length",Fe(e.length)),new Proxy(e,{defineProperty(a,c,u){"value"in u&&u.configurable!==!1&&u.enumerable!==!1&&u.writable!==!1||function(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}();var l=n.get(c);return l===void 0?l=i(()=>{var p=Fe(u.value);return n.set(c,p),p}):ee(l,u.value,!0),!0},deleteProperty(a,c){var u=n.get(c);if(u===void 0){if(c in a){const d=i(()=>Fe(oe));n.set(c,d),Dr(s)}}else{if(r&&typeof c=="string"){var l=n.get("length"),p=Number(c);Number.isInteger(p)&&p<l.v&&ee(l,p)}ee(u,oe),Dr(s)}return!0},get(a,c,u){var f;if(c===xe)return e;var l=n.get(c),p=c in a;if(l!==void 0||p&&!((f=He(a,c))!=null&&f.writable)||(l=i(()=>Fe(Et(p?a[c]:oe))),n.set(c,l)),l!==void 0){var d=V(l);return d===oe?void 0:d}return Reflect.get(a,c,u)},getOwnPropertyDescriptor(a,c){var u=Reflect.getOwnPropertyDescriptor(a,c);if(u&&"value"in u){var l=n.get(c);l&&(u.value=V(l))}else if(u===void 0){var p=n.get(c),d=p==null?void 0:p.v;if(p!==void 0&&d!==oe)return{enumerable:!0,configurable:!0,value:d,writable:!0}}return u},has(a,c){var p;if(c===xe)return!0;var u=n.get(c),l=u!==void 0&&u.v!==oe||Reflect.has(a,c);return(u!==void 0||z!==null&&(!l||(p=He(a,c))!=null&&p.writable))&&(u===void 0&&(u=i(()=>Fe(l?Et(a[c]):oe)),n.set(c,u)),V(u)===oe)?!1:l},set(a,c,u,l){var _;var p=n.get(c),d=c in a;if(r&&c==="length")for(var f=u;f<p.v;f+=1){var h=n.get(f+"");h!==void 0?ee(h,oe):f in a&&(h=i(()=>Fe(oe)),n.set(f+"",h))}p===void 0?d&&!((_=He(a,c))!=null&&_.writable)||(ee(p=i(()=>Fe(void 0)),Et(u)),n.set(c,p)):(d=p.v!==oe,ee(p,i(()=>Et(u))));var m=Reflect.getOwnPropertyDescriptor(a,c);if(m!=null&&m.set&&m.set.call(l,u),!d){if(r&&typeof c=="string"){var g=n.get("length"),v=Number(c);Number.isInteger(v)&&v>=g.v&&ee(g,v+1)}Dr(s)}return!0},ownKeys(a){V(s);var c=Reflect.ownKeys(a).filter(p=>{var d=n.get(p);return d===void 0||d.v!==oe});for(var[u,l]of n)l.v===oe||u in a||c.push(u);return c},setPrototypeOf(){(function(){throw new Error("https://svelte.dev/e/state_prototype_fixed")})()}})}function Dr(e,t=1){ee(e,e.v+t)}function ci(e){try{if(e!==null&&typeof e=="object"&&xe in e)return e[xe]}catch{}return e}function Ft(e){var t=_e|je,n=F!==null&&F.f&_e?F:null;return z===null||n!==null&&n.f&le?t|=le:z.f|=Na,{ctx:M,deps:null,effects:null,equals:ja,f:t,fn:e,reactions:null,rv:0,v:null,wv:0,parent:n??z,ac:null}}function yf(e){const t=Ft(e);return uc(t),t}function Wa(e){const t=Ft(e);return t.equals=Fa,t}function Ja(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)Te(t[n])}}function Ga(e){var t,n=z;Ve(function(r){for(var s=r.parent;s!==null;){if(!(s.f&_e))return s;s=s.parent}return null}(e));try{Ja(e),t=fc(e)}finally{Ve(n)}return t}function Ka(e){var t=Ga(e);e.equals(t)||(e.v=t,e.wv=lc()),Ut||ye(e,(Ue||e.f&le)&&e.deps!==null?Xe:fe)}const an=new Map;function lr(e,t){return{f:0,v:e,reactions:null,equals:ja,rv:0,wv:0}}function Fe(e,t){const n=lr(e);return uc(n),n}function os(e,t=!1,n=!0){var s;const r=lr(e);return t||(r.equals=Fa),qt&&n&&M!==null&&M.l!==null&&((s=M.l).s??(s.s=[])).push(r),r}function bf(e,t){return ee(e,$e(()=>V(e))),t}function ee(e,t,n=!1){return F!==null&&(!we||F.f&ii)&&ur()&&F.f&(_e|As|ii)&&((K==null?void 0:K.reaction)!==F||!K.sources.includes(e))&&function(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}(),Va(e,n?Et(t):t)}function Va(e,t){if(!e.equals(t)){var n=e.v;Ut?an.set(e,t):an.set(e,n),e.v=t,e.f&_e&&(e.f&je&&Ga(e),ye(e,e.f&le?Xe:fe)),e.wv=lc(),Ya(e,je),ur()&&z!==null&&z.f&fe&&!(z.f&(qe|jt))&&(pe===null?function(r){pe=r}([e]):pe.push(e))}return t}function ui(e,t=1){var n=V(e),r=t===1?n++:n--;return ee(e,n),r}function Ya(e,t){var n=e.reactions;if(n!==null)for(var r=ur(),s=n.length,o=0;o<s;o++){var i=n[o],a=i.f;a&je||(r||i!==z)&&(ye(i,t),a&(fe|le)&&(a&_e?Ya(i,Xe):pr(i)))}}var li,ud,Xa,Za,Qa;function Ns(e=""){return document.createTextNode(e)}function Rt(e){return Za.call(e)}function Ls(e){return Qa.call(e)}function ld(e,t){return Rt(e)}function dd(e,t){var n=Rt(e);return n instanceof Comment&&n.data===""?Ls(n):n}function Sf(e,t=1,n=!1){let r=e;for(;t--;)r=Ls(r);return r}function wf(e){e.textContent=""}function ec(e){z===null&&F===null&&function(t){throw new Error("https://svelte.dev/e/effect_orphan")}(),F!==null&&F.f&le&&z===null&&function(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}(),Ut&&function(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}()}function Ze(e,t,n,r=!0){var s=z,o={ctx:M,deps:null,nodes_start:null,nodes_end:null,f:e|je,first:null,fn:t,last:null,next:null,parent:s,b:s&&s.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{fr(o),o.f|=Da}catch(a){throw Te(o),a}else t!==null&&pr(o);if(!(n&&o.deps===null&&o.first===null&&o.nodes_start===null&&o.teardown===null&&!(o.f&(Na|Os)))&&r&&(s!==null&&function(a,c){var u=c.last;u===null?c.last=c.first=a:(u.next=a,a.prev=u,c.last=a)}(o,s),F!==null&&F.f&_e)){var i=F;(i.effects??(i.effects=[])).push(o)}return o}function Sn(e){const t=Ze(yn,null,!1);return ye(t,fe),t.teardown=e,t}function is(e){if(ec(),!(z!==null&&z.f&qe&&M!==null&&!M.m))return tc(e);var t=M;(t.e??(t.e=[])).push({fn:e,effect:z,reaction:F})}function tc(e){return Ze(Ps|Ds,e,!1)}function Ms(e){return Ze(Ps,e,!1)}function fd(e,t){var n=M,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=js(()=>{e(),r.ran||(r.ran=!0,ee(n.l.r2,!0),$e(t))})}function pd(){var e=M;js(()=>{if(V(e.l.r2)){for(var t of e.l.r1){var n=t.effect;n.f&fe&&ye(n,Xe),wn(n)&&fr(n),t.ran=!1}e.l.r2.v=!1}})}function js(e){return Ze(yn,e,!0)}function md(e,t=[],n=Ft){const r=t.map(n);return dr(()=>e(...r.map(V)))}function dr(e,t=0){return Ze(yn|As|t,e,!0)}function cn(e,t=!0){return Ze(yn|qe,e,!0,t)}function nc(e){var t=e.teardown;if(t!==null){const n=Ut,r=F;fi(!0),ke(null);try{t.call(null)}finally{fi(n),ke(r)}}}function rc(e,t=!1){var s;var n=e.first;for(e.first=e.last=null;n!==null;){(s=n.ac)==null||s.abort(Ma);var r=n.next;n.f&jt?n.parent=null:Te(n,t),n=r}}function Te(e,t=!0){var n=!1;(t||e.f&ed)&&e.nodes_start!==null&&e.nodes_end!==null&&(hd(e.nodes_start,e.nodes_end),n=!0),rc(e,t&&!n),Xn(e,0),ye(e,Cs);var r=e.transitions;if(r!==null)for(const o of r)o.stop();nc(e);var s=e.parent;s!==null&&s.first!==null&&sc(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=e.ac=null}function hd(e,t){for(;e!==null;){var n=e===t?null:Ls(e);e.remove(),e=n}}function sc(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function as(e,t){var n=[];oc(e,n,!0),gd(n,()=>{Te(e),t&&t()})}function gd(e,t){var n=e.length;if(n>0){var r=()=>--n||t();for(var s of e)s.out(r)}else t()}function oc(e,t,n){if(!(e.f&Ct)){if(e.f^=Ct,e.transitions!==null)for(const o of e.transitions)(o.is_global||n)&&t.push(o);for(var r=e.first;r!==null;){var s=r.next;oc(r,t,!!(r.f&Rs||r.f&qe)&&n),r=s}}}function di(e){ic(e,!0)}function ic(e,t){if(e.f&Ct){e.f^=Ct;for(var n=e.first;n!==null;){var r=n.next;ic(n,!!(n.f&Rs||n.f&qe)&&t),n=r}if(e.transitions!==null)for(const s of e.transitions)(s.is_global||t)&&s.in()}}let un=[],Nr=[];function ac(){var e=un;un=[],on(e)}function qs(e){un.length===0&&queueMicrotask(ac),un.push(e)}function vd(){var e;un.length>0&&ac(),Nr.length>0&&(e=Nr,Nr=[],on(e))}function cc(e,t){for(;t!==null;){if(t.f&Os)try{return void t.b.error(e)}catch{}t=t.parent}throw e}let ln=!1,dn=null,ut=!1,Ut=!1;function fi(e){Ut=e}let Yt=[],F=null,we=!1;function ke(e){F=e}let z=null;function Ve(e){z=e}let K=null;function uc(e){F!==null&&F.f&ss&&(K===null?K={reaction:F,sources:[e]}:K.sources.push(e))}let Q=null,ae=0,pe=null,Vn=1,Yn=0,Ue=!1,tt=null;function lc(){return++Vn}function wn(e){var p;var t=e.f;if(t&je)return!0;if(t&Xe){var n=e.deps,r=!!(t&le);if(n!==null){var s,o,i=!!(t&Kn),a=r&&z!==null&&!Ue,c=n.length;if(i||a){var u=e,l=u.parent;for(s=0;s<c;s++)o=n[s],!i&&((p=o==null?void 0:o.reactions)!=null&&p.includes(u))||(o.reactions??(o.reactions=[])).push(u);i&&(u.f^=Kn),!a||l===null||l.f&le||(u.f^=le)}for(s=0;s<c;s++)if(wn(o=n[s])&&Ka(o),o.wv>e.wv)return!0}r&&(z===null||Ue)||ye(e,fe)}return!1}function dc(e,t,n=!0){var r=e.reactions;if(r!==null)for(var s=0;s<r.length;s++){var o=r[s];(K==null?void 0:K.reaction)===F&&K.sources.includes(e)||(o.f&_e?dc(o,t,!1):t===o&&(n?ye(o,je):o.f&fe&&ye(o,Xe),pr(o)))}}function fc(e){var f;var t=Q,n=ae,r=pe,s=F,o=Ue,i=K,a=M,c=we,u=e.f;Q=null,ae=0,pe=null,Ue=!!(u&le)&&(we||!ut||F===null),F=u&(qe|jt)?null:e,K=null,ai(e.ctx),we=!1,Yn++,e.f|=ss,e.ac!==null&&(e.ac.abort(Ma),e.ac=null);try{var l=(0,e.fn)(),p=e.deps;if(Q!==null){var d;if(Xn(e,ae),p!==null&&ae>0)for(p.length=ae+Q.length,d=0;d<Q.length;d++)p[ae+d]=Q[d];else e.deps=p=Q;if(!Ue||u&_e&&e.reactions!==null)for(d=ae;d<p.length;d++)((f=p[d]).reactions??(f.reactions=[])).push(e)}else p!==null&&ae<p.length&&(Xn(e,ae),p.length=ae);if(ur()&&pe!==null&&!we&&p!==null&&!(e.f&(_e|Xe|je)))for(d=0;d<pe.length;d++)dc(pe[d],e);return s!==null&&s!==e&&(Yn++,pe!==null&&(r===null?r=pe:r.push(...pe))),l}catch(h){(function(m){var g=z;if(g.f&Da)cc(m,g);else{if(!(g.f&Os))throw m;g.fn(m)}})(h)}finally{Q=t,ae=n,pe=r,F=s,Ue=o,K=i,ai(a),we=c,e.f^=ss}}function _d(e,t){let n=t.reactions;if(n!==null){var r=Vl.call(n,e);if(r!==-1){var s=n.length-1;s===0?n=t.reactions=null:(n[r]=n[s],n.pop())}}n===null&&t.f&_e&&(Q===null||!Q.includes(t))&&(ye(t,Xe),t.f&(le|Kn)||(t.f^=Kn),Ja(t),Xn(t,0))}function Xn(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)_d(e,n[r])}function fr(e){var t=e.f;if(!(t&Cs)){ye(e,fe);var n=z,r=ut;z=e,ut=!0;try{t&As?function(o){for(var i=o.first;i!==null;){var a=i.next;i.f&qe||Te(i),i=a}}(e):rc(e),nc(e);var s=fc(e);e.teardown=typeof s=="function"?s:null,e.wv=Vn,Kl&&nd&&e.f&je&&e.deps}finally{ut=r,z=n}}}function yd(){try{(function(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")})()}catch(e){if(dn===null)throw e;cc(e,dn)}}function pc(){var e=ut;try{var t=0;for(ut=!0;Yt.length>0;){t++>1e3&&yd();var n=Yt,r=n.length;Yt=[];for(var s=0;s<r;s++)bd(Sd(n[s]));an.clear()}}finally{ln=!1,ut=e,dn=null}}function bd(e){var t=e.length;if(t!==0){for(var n=0;n<t;n++){var r=e[n];if(!(r.f&(Cs|Ct))&&wn(r)){var s=Vn;if(fr(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?sc(r):r.fn=null),Vn>s&&r.f&Ds)break}}for(;n<t;n+=1)pr(e[n])}}function pr(e){ln||(ln=!0,queueMicrotask(pc));for(var t=dn=e;t.parent!==null;){var n=(t=t.parent).f;if(n&(jt|qe)){if(!(n&fe))return;t.f^=fe}}Yt.push(t)}function Sd(e){for(var t=[],n=e;n!==null;){var r=n.f,s=!!(r&(qe|jt));if(!(s&&r&fe||r&Ct)){r&Ps?t.push(n):s?n.f^=fe:wn(n)&&fr(n);var o=n.first;if(o!==null){n=o;continue}}var i=n.parent;for(n=n.next;n===null&&i!==null;)n=i.next,i=i.parent}return t}function wd(e){for(;;){if(vd(),Yt.length===0)return ln=!1,void(dn=null);ln=!0,pc()}}async function Ef(){await Promise.resolve(),wd()}function V(e){var t=!!(e.f&_e);if(tt!==null&&tt.add(e),F===null||we){if(t&&e.deps===null&&e.effects===null){var n=e,r=n.parent;r===null||r.f&le||(n.f^=le)}}else if((K==null?void 0:K.reaction)!==F||!(K!=null&&K.sources.includes(e))){var s=F.deps;e.rv<Yn&&(e.rv=Yn,Q===null&&s!==null&&s[ae]===e?ae++:Q===null?Q=[e]:Ue&&Q.includes(e)||Q.push(e))}return t&&wn(n=e)&&Ka(n),Ut&&an.has(e)?an.get(e):e.v}function xf(e){var t=function(r){var s=tt;tt=new Set;var o,i=tt;try{if($e(r),s!==null)for(o of tt)s.add(o)}finally{tt=s}return i}(()=>$e(e));for(var n of t)Va(n,n.v)}function $e(e){var t=we;try{return we=!0,e()}finally{we=t}}const Ed=-7169;function ye(e,t){e.f=e.f&Ed|t}function xd(e,t){var n={};for(var r in e)t.includes(r)||(n[r]=e[r]);return n}function mc(e){if(typeof e=="object"&&e&&!(e instanceof EventTarget)){if(xe in e)cs(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&xe in n&&cs(n)}}}function cs(e,t=new Set){if(!(typeof e!="object"||e===null||e instanceof EventTarget||t.has(e))){t.add(e),e instanceof Date&&e.getTime();for(let r in e)try{cs(e[r],t)}catch{}const n=Is(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=Ra(n);for(let s in r){const o=r[s].get;if(o)try{o.call(e)}catch{}}}}}function Td(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const kd=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function $d(e){return kd.includes(e)}const Id={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function Pd(e){return e=e.toLowerCase(),Id[e]??e}const Ad=["touchstart","touchmove"];function Od(e){return Ad.includes(e)}function Cd(e,t){if(t){const n=document.body;e.autofocus=!0,qs(()=>{document.activeElement===n&&e.focus()})}}let pi=!1;function Tf(e,t,n,r=!0){for(var s of(r&&n(),t))e.addEventListener(s,n);Sn(()=>{for(var o of t)e.removeEventListener(o,n)})}function hc(e){var t=F,n=z;ke(null),Ve(null);try{return e()}finally{ke(t),Ve(n)}}function kf(e,t,n,r=n){e.addEventListener(t,()=>hc(n));const s=e.__on_r;e.__on_r=s?()=>{s(),r(!0)}:()=>r(!0),pi||(pi=!0,document.addEventListener("reset",o=>{Promise.resolve().then(()=>{var i;if(!o.defaultPrevented)for(const a of o.target.elements)(i=a.__on_r)==null||i.call(a)})},{capture:!0}))}const gc=new Set,us=new Set;function vc(e,t,n,r={}){function s(o){if(r.capture||Wt.call(t,o),!o.cancelBubble)return hc(()=>n==null?void 0:n.call(this,o))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?qs(()=>{t.addEventListener(e,s,r)}):t.addEventListener(e,s,r),s}function $f(e,t,n,r,s){var o={capture:r,passive:s},i=vc(e,t,n,o);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&Sn(()=>{t.removeEventListener(e,i,o)})}function Rd(e){for(var t=0;t<e.length;t++)gc.add(e[t]);for(var n of us)n(e)}function Wt(e){var _;var t=this,n=t.ownerDocument,r=e.type,s=((_=e.composedPath)==null?void 0:_.call(e))||[],o=s[0]||e.target,i=0,a=e.__root;if(a){var c=s.indexOf(a);if(c!==-1&&(t===document||t===window))return void(e.__root=t);var u=s.indexOf(t);if(u===-1)return;c<=u&&(i=c)}if((o=s[i]||e.target)!==t){Ca(e,"currentTarget",{configurable:!0,get:()=>o||n});var l=F,p=z;ke(null),Ve(null);try{for(var d,f=[];o!==null;){var h=o.assignedSlot||o.parentNode||o.host||null;try{var m=o["__"+r];if(m!=null&&(!o.disabled||e.target===o))if(cr(m)){var[g,...v]=m;g.apply(o,[e,...v])}else m.call(o,e)}catch(S){d?f.push(S):d=S}if(e.cancelBubble||h===t||h===null)break;o=h}if(d){for(let S of f)queueMicrotask(()=>{throw S});throw d}}finally{e.__root=t,delete e.currentTarget,ke(l),Ve(p)}}}function _c(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function fn(e,t){var n=z;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function yc(e,t){var n,r=!!(1&t),s=!!(2&t),o=!e.startsWith("<!>");return()=>{n===void 0&&(n=_c(o?e:"<!>"+e),r||(n=Rt(n)));var i=s||Xa?document.importNode(n,!0):n.cloneNode(!0);return r?fn(Rt(i),i.lastChild):fn(i,i),i}}function If(e,t){return function(n,r,s="svg"){var o,i=`<${s}>${n.startsWith("<!>")?"<!>"+n:n}</${s}>`;return()=>{if(!o){var a=Rt(_c(i));o=Rt(a)}var c=o.cloneNode(!0);return fn(c,c),c}}(e,0,"svg")}function Pf(e=""){var t=Ns(e+"");return fn(t,t),t}function Dd(){var e=document.createDocumentFragment(),t=document.createComment(""),n=Ns();return e.append(t,n),fn(t,n),e}function ls(e,t){e!==null&&e.before(t)}let ds=!0;function Af(e){ds=e}function Of(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=n,e.nodeValue=n+"")}function Cf(e,t){return function(n,{target:r,anchor:s,props:o={},events:i,context:a,intro:c=!0}){(function(){if(li===void 0){li=window,ud=document,Xa=/Firefox/.test(navigator.userAgent);var f=Element.prototype,h=Node.prototype,m=Text.prototype;Za=He(h,"firstChild").get,Qa=He(h,"nextSibling").get,oi(f)&&(f.__click=void 0,f.__className=void 0,f.__attributes=null,f.__style=void 0,f.__e=void 0),oi(m)&&(m.__t=void 0)}})();var u=new Set,l=f=>{for(var h=0;h<f.length;h++){var m=f[h];if(!u.has(m)){u.add(m);var g=Od(m);r.addEventListener(m,Wt,{passive:g});var v=_t.get(m);v===void 0?(document.addEventListener(m,Wt,{passive:g}),_t.set(m,1)):_t.set(m,v+1)}}};l(Yl(gc)),us.add(l);var p=void 0,d=function(f){const h=Ze(jt,f,!0);return(m={})=>new Promise(g=>{m.outro?as(h,()=>{Te(h),g(void 0)}):(Te(h),g(void 0))})}(()=>{var f=s??r.appendChild(Ns());return cn(()=>{a&&(Ha({}),M.c=a),i&&(o.$$events=i),ds=c,p=n(f,o)||{},ds=!0,a&&Ba()}),()=>{var g;for(var h of u){r.removeEventListener(h,Wt);var m=_t.get(h);--m==0?(document.removeEventListener(h,Wt),_t.delete(h)):_t.set(h,m)}us.delete(l),f!==s&&((g=f.parentNode)==null||g.removeChild(f))}});return fs.set(p,d),p}(e,t)}const _t=new Map;let fs=new WeakMap;function Rf(e,t){const n=fs.get(e);return n?(fs.delete(e),n(t)):Promise.resolve()}function Nd(e,t,[n,r]=[0,0]){var s=e,o=null,i=null,a=oe,c=!1;const u=(p,d=!0)=>{c=!0,l(d,p)},l=(p,d)=>{a!==(a=p)&&(a?(o?di(o):d&&(o=cn(()=>d(s))),i&&as(i,()=>{i=null})):(i?di(i):d&&(i=cn(()=>d(s,[n+1,r]))),o&&as(o,()=>{o=null})))};dr(()=>{c=!1,t(u),c||l(null,null)},n>0?Rs:0)}function Ld(e,t,n,r,s){var a;var o=(a=t.$$slots)==null?void 0:a[n],i=!1;o===!0&&(o=t[n==="default"?"children":n],i=!0),o===void 0?s!==null&&s(e):o(e,i?()=>r:r)}function Df(e){const t={};e.children&&(t.default=!0);for(const n in e.$$slots)t[n]=!0;return t}function Md(e,t){var n,r=void 0;dr(()=>{r!==(r=t())&&(n&&(Te(n),n=null),r&&(n=cn(()=>{Ms(()=>r(e))})))})}function bc(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=bc(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function jd(e){return typeof e=="object"?function(){for(var t,n,r=0,s="",o=arguments.length;r<o;r++)(t=arguments[r])&&(n=bc(t))&&(s&&(s+=" "),s+=n);return s}(e):e??""}const mi=[...` 	
\r\f \v\uFEFF`];function hi(e,t=!1){var n=t?" !important;":";",r="";for(var s in e){var o=e[s];o!=null&&o!==""&&(r+=" "+s+": "+o+n)}return r}function Lr(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function Sc(e,t,n,r,s,o){var i=e.__className;if(i!==n||i===void 0){var a=function(l,p,d){var f=l==null?"":""+l;if(p&&(f=f?f+" "+p:p),d){for(var h in d)if(d[h])f=f?f+" "+h:h;else if(f.length)for(var m=h.length,g=0;(g=f.indexOf(h,g))>=0;){var v=g+m;g!==0&&!mi.includes(f[g-1])||v!==f.length&&!mi.includes(f[v])?g=v:f=(g===0?"":f.substring(0,g))+f.substring(v+1)}}return f===""?null:f}(n,r,o);a==null?e.removeAttribute("class"):t?e.className=a:e.setAttribute("class",a),e.__className=n}else if(o&&s!==o)for(var c in o){var u=!!o[c];s!=null&&u===!!s[c]||e.classList.toggle(c,u)}return o}function Mr(e,t={},n,r){for(var s in n){var o=n[s];t[s]!==o&&(n[s]==null?e.style.removeProperty(s):e.style.setProperty(s,o,r))}}function qd(e,t,n,r){if(e.__style!==t){var s=function(o,i){if(i){var a,c,u="";if(Array.isArray(i)?(a=i[0],c=i[1]):a=i,o){o=String(o).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var l=!1,p=0,d=!1,f=[];a&&f.push(...Object.keys(a).map(Lr)),c&&f.push(...Object.keys(c).map(Lr));var h=0,m=-1;const S=o.length;for(var g=0;g<S;g++){var v=o[g];if(d?v==="/"&&o[g-1]==="*"&&(d=!1):l?l===v&&(l=!1):v==="/"&&o[g+1]==="*"?d=!0:v==='"'||v==="'"?l=v:v==="("?p++:v===")"&&p--,!d&&l===!1&&p===0){if(v===":"&&m===-1)m=g;else if(v===";"||g===S-1){if(m!==-1){var _=Lr(o.substring(h,m).trim());f.includes(_)||(v!==";"&&g++,u+=" "+o.substring(h,g).trim()+";")}h=g+1,m=-1}}}}return a&&(u+=hi(a)),c&&(u+=hi(c,!0)),(u=u.trim())===""?null:u}return o==null?null:String(o)}(t,r);s==null?e.removeAttribute("style"):e.style.cssText=s,e.__style=t}else r&&(Array.isArray(r)?(Mr(e,n==null?void 0:n[0],r[0]),Mr(e,n==null?void 0:n[1],r[1],"important")):Mr(e,n,r));return r}function jr(e,t,n=!1){if(e.multiple){if(t==null)return;if(!cr(t))return void console.warn("https://svelte.dev/e/select_multiple_invalid_value");for(var r of e.options)r.selected=t.includes(gi(r))}else{for(r of e.options){var s=gi(r);if(o=s,i=t,Object.is(ci(o),ci(i)))return void(r.selected=!0)}var o,i;n&&t===void 0||(e.selectedIndex=-1)}}function gi(e){return"__value"in e?e.__value:e.value}const St=Symbol("class"),Bt=Symbol("style"),wc=Symbol("is custom element"),Ec=Symbol("is html");function Nf(e,t){var n=Fs(e);n.value!==(n.value=t??void 0)&&(e.value!==t||t===0&&e.nodeName==="PROGRESS")&&(e.value=t??"")}function Fd(e,t){t?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function vi(e,t,n,r){var s=Fs(e);s[t]!==(s[t]=n)&&(t==="loading"&&(e[td]=n),n==null?e.removeAttribute(t):typeof n!="string"&&xc(e).includes(t)?e[t]=n:e.setAttribute(t,n))}function Ud(e,t,n=[],r,s=!1,o=Ft){const i=n.map(o);var a=void 0,c={},u=e.nodeName==="SELECT",l=!1;if(dr(()=>{var d=t(...i.map(V)),f=function(m,g,v,_,S=!1){var y=Fs(m),b=y[wc],T=!y[Ec],w=g||{},L=m.tagName==="OPTION";for(var E in g)E in v||(v[E]=null);v.class?v.class=jd(v.class):(_||v[St])&&(v.class=null),v[Bt]&&(v.style??(v.style=null));var R=xc(m);for(const $ in v){let C=v[$];if(L&&$==="value"&&C==null)m.value=m.__value="",w[$]=C;else if($!=="class")if($!=="style"){var D=w[$];if(C!==D||C===void 0&&m.hasAttribute($)){w[$]=C;var k=$[0]+$[1];if(k!=="$$")if(k==="on"){const U={},re="$$"+$;let J=$.slice(2);var A=$d(J);if(Td(J)&&(J=J.slice(0,-7),U.capture=!0),!A&&D){if(C!=null)continue;m.removeEventListener(J,w[re],U),w[re]=null}if(C!=null)if(A)m[`__${J}`]=C,Rd([J]);else{let be=function(ie){w[$].call(this,ie)};w[re]=vc(J,m,be,U)}else A&&(m[`__${J}`]=void 0)}else if($==="style")vi(m,$,C);else if($==="autofocus")Cd(m,!!C);else if(b||$!=="__value"&&($!=="value"||C==null))if($==="selected"&&L)Fd(m,C);else{var I=$;T||(I=Pd(I));var P=I==="defaultValue"||I==="defaultChecked";if(C!=null||b||P)P||R.includes(I)&&(b||typeof C!="string")?m[I]=C:typeof C!="function"&&vi(m,I,C);else if(y[$]=null,I==="value"||I==="checked"){let U=m;const re=g===void 0;if(I==="value"){let J=U.defaultValue;U.removeAttribute(I),U.defaultValue=J,U.value=U.__value=re?J:null}else{let J=U.defaultChecked;U.removeAttribute(I),U.defaultChecked=J,U.checked=!!re&&J}}else m.removeAttribute($)}else m.value=m.__value=C}}else qd(m,C,g==null?void 0:g[Bt],v[Bt]),w[$]=C,w[Bt]=v[Bt];else Sc(m,m.namespaceURI==="http://www.w3.org/1999/xhtml",C,_,g==null?void 0:g[St],v[St]),w[$]=C,w[St]=v[St]}return w}(e,a,d,r,s);l&&u&&"value"in d&&jr(e,d.value);for(let m of Object.getOwnPropertySymbols(c))d[m]||Te(c[m]);for(let m of Object.getOwnPropertySymbols(d)){var h=d[m];m.description!==cd||a&&h===a[m]||(c[m]&&Te(c[m]),c[m]=cn(()=>Md(e,()=>h))),f[m]=h}a=f}),u){var p=e;Ms(()=>{jr(p,a.value,!0),function(d){var f=new MutationObserver(()=>{jr(d,d.__value)});f.observe(d,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),Sn(()=>{f.disconnect()})}(p)})}l=!0}function Fs(e){return e.__attributes??(e.__attributes={[wc]:e.nodeName.includes("-"),[Ec]:e.namespaceURI===ad})}var _i=new Map;function xc(e){var t,n=_i.get(e.nodeName);if(n)return n;_i.set(e.nodeName,n=[]);for(var r=e,s=Element.prototype;s!==r;){for(var o in t=Ra(r))t[o].set&&n.push(o);r=Is(r)}return n}function yi(e,t){return e===t||(e==null?void 0:e[xe])===t}function Lf(e={},t,n,r){return Ms(()=>{var s,o;return js(()=>{s=o,o=(r==null?void 0:r())||[],$e(()=>{e!==n(...o)&&(t(e,...o),s&&yi(n(...s),e)&&t(null,...s))})}),()=>{qs(()=>{o&&yi(n(...o),e)&&t(null,...o)})}}),e}function Hd(e=!1){const t=M,n=t.l.u;if(!n)return;let r=()=>mc(t.s);if(e){let o=0,i={};const a=Ft(()=>{let c=!1;const u=t.s;for(const l in u)u[l]!==i[l]&&(i[l]=u[l],c=!0);return c&&o++,o});r=()=>V(a)}var s;n.b.length&&(s=()=>{bi(t,r),on(n.b)},ec(),Ze(yn|Ds,s,!0)),is(()=>{const o=$e(()=>n.m.map(Ql));return()=>{for(const i of o)typeof i=="function"&&i()}}),n.a.length&&is(()=>{bi(t,r),on(n.a)})}function bi(e,t){if(e.l.s)for(const n of e.l.s)V(n);t()}function Us(e,t,n){if(e==null)return t(void 0),n&&n(void 0),De;const r=$e(()=>e.subscribe(t,n));return r.unsubscribe?()=>r.unsubscribe():r}const yt=[];function Bd(e,t){return{subscribe:Tc(e,t).subscribe}}function Tc(e,t=De){let n=null;const r=new Set;function s(i){if(qa(e,i)&&(e=i,n)){const a=!yt.length;for(const c of r)c[1](),yt.push(c,e);if(a){for(let c=0;c<yt.length;c+=2)yt[c][0](yt[c+1]);yt.length=0}}}function o(i){s(i(e))}return{set:s,update:o,subscribe:function(i,a=De){const c=[i,a];return r.add(c),r.size===1&&(n=t(s,o)||De),i(e),()=>{r.delete(c),r.size===0&&n&&(n(),n=null)}}}}function Mf(e,t,n){const r=!Array.isArray(e),s=r?[e]:e;if(!s.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=t.length<2;return Bd(n,(i,a)=>{let c=!1;const u=[];let l=0,p=De;const d=()=>{if(l)return;p();const h=t(r?u[0]:u,i,a);o?i(h):p=typeof h=="function"?h:De},f=s.map((h,m)=>Us(h,g=>{u[m]=g,l&=~(1<<m),c&&d()},()=>{l|=1<<m}));return c=!0,d(),function(){on(f),p(),c=!1}})}function jf(e){return{subscribe:e.subscribe.bind(e)}}function zd(e){let t;return Us(e,n=>t=n)(),t}let Jt=!1,ps=Symbol();function qf(e,t,n){const r=n[t]??(n[t]={store:null,source:os(void 0),unsubscribe:De});if(r.store!==e&&!(ps in n))if(r.unsubscribe(),r.store=e??null,e==null)r.source.v=void 0,r.unsubscribe=De;else{var s=!0;r.unsubscribe=Us(e,o=>{s?r.source.v=o:ee(r.source,o)}),s=!1}return e&&ps in n?zd(e):V(r.source)}function Ff(e,t,n){let r=n[t];return r&&r.store!==e&&(r.unsubscribe(),r.unsubscribe=De),e}function Uf(e,t){return e.set(t),t}function Hf(){const e={};return[e,function(){Sn(()=>{for(var t in e)e[t].unsubscribe();Ca(e,ps,{enumerable:!1,value:!0})})}]}function Bf(e,t,n){return e.set(n),t}function zf(){Jt=!0}const Wd={get(e,t){if(!e.exclude.includes(t))return e.props[t]},set:(e,t)=>!1,getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t))return t in e.props?{enumerable:!0,configurable:!0,value:e.props[t]}:void 0},has:(e,t)=>!e.exclude.includes(t)&&t in e.props,ownKeys:e=>Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))};function Wf(e,t,n){return new Proxy({props:e,exclude:t},Wd)}const Jd={get(e,t){if(!e.exclude.includes(t))return V(e.version),t in e.special?e.special[t]():e.props[t]},set:(e,t,n)=>(t in e.special||(e.special[t]=Ee({get[t](){return e.props[t]}},t,Ua)),e.special[t](n),ui(e.version),!0),getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t))return t in e.props?{enumerable:!0,configurable:!0,value:e.props[t]}:void 0},deleteProperty:(e,t)=>(e.exclude.includes(t)||(e.exclude.push(t),ui(e.version)),!0),has:(e,t)=>!e.exclude.includes(t)&&t in e.props,ownKeys:e=>Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))};function Si(e,t){return new Proxy({props:e,exclude:t,special:{},version:lr(0)},Jd)}const Gd={get(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(Ht(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r)return r[t]}},set(e,t,n){let r=e.props.length;for(;r--;){let s=e.props[r];Ht(s)&&(s=s());const o=He(s,t);if(o&&o.set)return o.set(n),!0}return!1},getOwnPropertyDescriptor(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(Ht(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r){const s=He(r,t);return s&&!s.configurable&&(s.configurable=!0),s}}},has(e,t){if(t===xe||t===La)return!1;for(let n of e.props)if(Ht(n)&&(n=n()),n!=null&&t in n)return!0;return!1},ownKeys(e){const t=[];for(let n of e.props)if(Ht(n)&&(n=n()),n){for(const r in n)t.includes(r)||t.push(r);for(const r of Object.getOwnPropertySymbols(n))t.includes(r)||t.push(r)}return t}};function Jf(...e){return new Proxy({props:e},Gd)}function Ee(e,t,n,r){var v;var s,o,i=!qt||!!(n&sd),a=!!(n&od),c=!!(n&id),u=r,l=!0,p=()=>(l&&(l=!1,u=c?$e(r):r),u);if(a){var d=xe in e||La in e;s=((v=He(e,t))==null?void 0:v.set)??(d&&t in e?_=>e[t]=_:void 0)}var f,h=!1;if(a?[o,h]=function(_){var S=Jt;try{return Jt=!1,[_(),Jt]}finally{Jt=S}}(()=>e[t]):o=e[t],o===void 0&&r!==void 0&&(o=p(),s&&(i&&function(_){throw new Error("https://svelte.dev/e/props_invalid_value")}(),s(o))),f=i?()=>{var _=e[t];return _===void 0?p():(l=!0,_)}:()=>{var _=e[t];return _!==void 0&&(u=void 0),_===void 0?u:_},i&&!(n&Ua))return f;if(s){var m=e.$$legacy;return function(_,S){return arguments.length>0?(i&&S&&!m&&!h||s(S?f():_),_):f()}}var g=(n&rd?Ft:Wa)(f);return a&&V(g),function(_,S){var b;if(arguments.length>0){const T=S?V(g):i&&a?Et(_):_;return ee(g,T),u!==void 0&&(u=T),_}return y=g,(b=y.ctx)!=null&&b.d?g.v:V(g);var y}}function Kd(e){M===null&&bn(),qt&&M.l!==null?kc(M).m.push(e):is(()=>{const t=$e(e);if(typeof t=="function")return t})}function Gf(e){M===null&&bn(),Kd(()=>()=>$e(e))}function Kf(){const e=M;return e===null&&bn(),(t,n,r)=>{var o;const s=(o=e.s.$$events)==null?void 0:o[t];if(s){const i=cr(s)?s.slice():[s],a=function(c,u,{bubbles:l=!1,cancelable:p=!1}={}){return new CustomEvent(c,{detail:u,bubbles:l,cancelable:p})}(t,n,r);for(const c of i)c.call(e.x,a);return!a.defaultPrevented}return!0}}function Vf(e){M===null&&bn(),M.l===null&&function(t){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}(),kc(M).b.push(e)}function kc(e){var t=e.l;return t.u??(t.u={a:[],b:[],m:[]})}var Yf=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Xf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Vd(e){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let t=0,n=performance.now(),r=60;const s=[];let o=0;const i=e.lowFramerateThreshold,a=e.slowInpThreshold;if(requestAnimationFrame(function c(u){const l=u-n;if(t++,l>1e3){r=1e3*t/l,t=0,n=u,s.push(r),s.length>10&&s.shift();const p=s.reduce((d,f)=>d+f,0)/s.length;if(r<i){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${p.toFixed(1)} fps`);const d=ft({name:"slow_framerate",op:"performance.monitoring",attributes:{"performance.fps":r,"performance.avg_fps":p,"performance.threshold":i,"performance.is_critical":r<15,"webview.url":window.location.href}});d.setStatus({code:2,message:`Slow framerate: ${r.toFixed(1)} fps`}),d.end()}}requestAnimationFrame(c)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(c=>{(u=>{const l=u.getEntries().filter(f=>"interactionId"in f&&"duration"in f&&f.startTime>0&&f.duration<1e6);if(l.length===0)return;l.sort((f,h)=>h.duration-f.duration);const p=Math.floor(.98*l.length),d=l[Math.min(p,l.length-1)].duration;if(d>a){console.error(`[Augment Performance] Slow INP detected: ${d.toFixed(1)} ms`);let f=null;const h=l[0];h&&"target"in h&&(f=h.target,console.error("[Augment Performance] Slow interaction target:",f,h));const m=ft({name:"slow_inp",op:"performance.monitoring",attributes:{"performance.inp":d,"performance.threshold":a,"performance.target":f?String(f):void 0,"webview.url":window.location.href}});m.setStatus({code:2,message:`Slow INP: ${d.toFixed(1)} ms`}),m.end(),d>o&&(o=d)}})(c)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(c){console.error("[Augment Performance] Error setting up INP monitoring:",c)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>o}const wi=16,Ei=200;function xi(){var e;return((e=window.augmentFlags)==null?void 0:e.enablePerformanceMonitoring)??!1}let Ti=!1;(function(){var n,r;const e=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var t;(t={enabled:xi(),lowFramerateThreshold:wi,slowInpThreshold:Ei}).enabled&&Vd({lowFramerateThreshold:t.lowFramerateThreshold||wi,slowInpThreshold:t.slowInpThreshold||Ei}),xi()&&!e&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var t,n;if(!((n=(t=window.augmentFlags)==null?void 0:t.sentry)!=null&&n.enabled))return;const e=window.augmentFlags.sentry;if(e)if(Ti)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const s={...r};ua(s,"svelte"),Ul(s)})({dsn:e.dsn,release:e.release,environment:e.environment,tracesSampleRate:e.tracesSampleRate||0,replaysSessionSampleRate:e.replaysSessionSampleRate||0,replaysOnErrorSampleRate:e.replaysOnErrorSampleRate||0,sampleRate:e.errorSampleRate||0,sendDefaultPii:e.sendDefaultPii!==void 0&&e.sendDefaultPii,integrations:(()=>{const r=[];return e.tracesSampleRate&&e.tracesSampleRate>0&&r.push(Gl()),r})(),beforeSend:r=>{var s;return(s=e.release)!=null&&s.endsWith("@999.999.999")?null:r}}),e.tags&&Object.entries(e.tags).forEach(([r,s])=>{(function(o,i){Ye().setTag(o,i)})(r,String(s))}),Ti=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();let Yd=document.documentElement;function ht(){return Yd??document.documentElement}var $c=(e=>(e.light="light",e.dark="dark",e))($c||{}),Ic=(e=>(e.regular="regular",e.highContrast="high-contrast",e))(Ic||{});const Zn="data-augment-theme-category",Qn="data-augment-theme-intensity";function Pc(){const e=ht().getAttribute(Zn);if(e&&Object.values($c).includes(e))return e}function Zf(e){e===void 0?ht().removeAttribute(Zn):ht().setAttribute(Zn,e)}function Ac(){const e=ht().getAttribute(Qn);if(e&&Object.values(Ic).includes(e))return e}function Qf(e){e===void 0?ht().removeAttribute(Qn):ht().setAttribute(Qn,e)}const ki=Tc(void 0);function Xd(e){const t=new MutationObserver(n=>{for(const r of n)if(r.type==="attributes"){e(Pc(),Ac());break}});return t.observe(ht(),{attributeFilter:[Zn,Qn],attributes:!0}),t}Xd((e,t)=>{ki.update(()=>({category:e,intensity:t}))}),ki.update(()=>({category:Pc(),intensity:Ac()}));function Zd(e){return e?{"data-ds-color":e}:{}}function ep(e){return{"data-ds-radius":e}}function tp(e,t,n){return n?{[`data-ds-${e}-${t}`]:!0,[`data-${t}`]:!0}:{}}var $i;typeof window<"u"&&(($i=window.__svelte??(window.__svelte={})).v??($i.v=new Set)).add("5"),qt=!0;var Qd=yc("<span><!></span>");function np(e,t){const n=Si(t,["children","$$slots","$$events","$$legacy"]),r=Si(n,["size","weight","type","color","truncate"]);Ha(t,!1);const s=os(),o=os();let i=Ee(t,"size",8,3),a=Ee(t,"weight",8,"regular"),c=Ee(t,"type",8,"default"),u=Ee(t,"color",24,()=>{}),l=Ee(t,"truncate",8,!1);fd(()=>(V(s),V(o),mc(r)),()=>{ee(s,r.class),ee(o,xd(r,["class"]))}),pd(),Hd();var p=Qd();Ud(p,(d,f)=>({...d,class:`c-text c-text--size-${i()??""} c-text--weight-${a()??""} c-text--type-${c()??""} c-text--color-${u()??""} ${V(s)??""}`,...V(o),[St]:f}),[()=>u()?Zd(u()):{},()=>({"c-text--has-color":u()!==void 0,"c-text--truncate":l()})],"svelte-zmgqjq"),Ld(ld(p),t,"default",{},null),ls(e,p),Ba()}var ef=yc('<div data-testid="spinner-augment"><div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div></div>');function rp(e,t){let n=Ee(t,"size",8,2),r=Ee(t,"loading",8,!0),s=Ee(t,"useCurrentColor",8,!1),o=Ee(t,"class",8,"");var i=Dd(),a=dd(i),c=u=>{var l=ef();let p;md(d=>p=Sc(l,1,`c-spinner c-spinner--size-${n()??""} ${o()??""}`,"svelte-abmqgo",p,d),[()=>({"c-spinner--current-color":s()})],Wa),ls(u,l)};Nd(a,u=>{r()&&u(c)}),ls(e,i)}export{pd as $,Ha as A,Kf as B,Ee as C,Hd as D,Rs as E,Dd as F,dd as G,Nd as H,Ba as I,yc as J,mc as K,$e as L,Sf as M,Ld as N,Ms as O,$f as P,md as Q,vi as R,qd as S,np as T,oe as U,Pf as V,Of as W,fd as X,ee as Y,xd as Z,V as _,Ud as a,Wf as a$,Zd as a0,St as a1,Hf as a2,Wa as a3,qf as a4,Ff as a5,Gf as a6,Jf as a7,Xd as a8,Pc as a9,zd as aA,of as aB,qa as aC,Mf as aD,bf as aE,yf as aF,jf as aG,Ef as aH,uf as aI,Yl as aJ,cr as aK,Ct as aL,lf as aM,af as aN,cf as aO,oc as aP,wf as aQ,gd as aR,df as aS,Ls as aT,hd as aU,_c as aV,Rt as aW,Ic as aX,Zf as aY,Qf as aZ,ep as a_,$c as aa,Sc as ab,Te as ac,hf as ad,fn as ae,Ns as af,z as ag,Af as ah,kf as ai,js as aj,Kd as ak,li as al,Lf as am,ds as an,As as ao,Da as ap,mf as aq,hc as ar,Ht as as,De as at,F as au,ff as av,pf as aw,ki as ax,rp as ay,Cf as az,ls as b,Uf as b0,jd as b1,He as b2,Sn as b3,gf as b4,Is as b5,Xl as b6,Bd as b7,sf as b8,xf as b9,Nf as ba,Bf as bb,ud as bc,tp as bd,Tf as be,Rf as bf,zf as bg,Vf as bh,rf as bi,is as bj,Et as bk,ui as bl,dr as c,nf as d,Va as e,If as f,Ve as g,ke as h,ur as i,ai as j,M as k,Si as l,os as m,cn as n,wd as o,as as p,qs as q,di as r,lr as s,ld as t,vf as u,_f as v,Tc as w,Yf as x,Xf as y,Df as z};
