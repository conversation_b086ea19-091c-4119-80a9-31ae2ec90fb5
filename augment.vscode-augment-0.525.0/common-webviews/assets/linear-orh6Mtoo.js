import{a$ as hr,b0 as B,b1 as K,b2 as mr,b3 as Q}from"./AugmentMessage-Dd8zwzVO.js";import{i as gr}from"./init-g68aIKmP.js";function C(r,t){return r==null||t==null?NaN:r<t?-1:r>t?1:r>=t?0:NaN}function pr(r,t){return r==null||t==null?NaN:t<r?-1:t>r?1:t>=r?0:NaN}function nr(r){let t,e,i;function n(o,a,u=0,s=o.length){if(u<s){if(t(a,a)!==0)return s;do{const l=u+s>>>1;e(o[l],a)<0?u=l+1:s=l}while(u<s)}return u}return r.length!==2?(t=C,e=(o,a)=>C(r(o),a),i=(o,a)=>r(o)-a):(t=r===C||r===pr?r:vr,e=r,i=r),{left:n,center:function(o,a,u=0,s=o.length){const l=n(o,a,u,s-1);return l>u&&i(o[l-1],a)>-i(o[l],a)?l-1:l},right:function(o,a,u=0,s=o.length){if(u<s){if(t(a,a)!==0)return s;do{const l=u+s>>>1;e(o[l],a)<=0?u=l+1:s=l}while(u<s)}return u}}}function vr(){return 0}const Mr=nr(C).right;nr(function(r){return r===null?NaN:+r}).center;const dr=Math.sqrt(50),yr=Math.sqrt(10),br=Math.sqrt(2);function D(r,t,e){const i=(t-r)/Math.max(0,e),n=Math.floor(Math.log10(i)),o=i/Math.pow(10,n),a=o>=dr?10:o>=yr?5:o>=br?2:1;let u,s,l;return n<0?(l=Math.pow(10,-n)/a,u=Math.round(r*l),s=Math.round(t*l),u/l<r&&++u,s/l>t&&--s,l=-l):(l=Math.pow(10,n)*a,u=Math.round(r/l),s=Math.round(t/l),u*l<r&&++u,s*l>t&&--s),s<u&&.5<=e&&e<2?D(r,t,2*e):[u,s,l]}function G(r,t,e){return D(r=+r,t=+t,e=+e)[2]}function wr(r,t,e){e=+e;const i=(t=+t)<(r=+r),n=i?G(t,r,e):G(r,t,e);return(i?-1:1)*(n<0?1/-n:n)}function Nr(r,t){t||(t=[]);var e,i=r?Math.min(t.length,r.length):0,n=t.slice();return function(o){for(e=0;e<i;++e)n[e]=r[e]*(1-o)+t[e]*o;return n}}function xr(r,t){var e,i=t?t.length:0,n=r?Math.min(i,r.length):0,o=new Array(n),a=new Array(i);for(e=0;e<n;++e)o[e]=Y(r[e],t[e]);for(;e<i;++e)a[e]=t[e];return function(u){for(e=0;e<n;++e)a[e]=o[e](u);return a}}function kr(r,t){var e=new Date;return r=+r,t=+t,function(i){return e.setTime(r*(1-i)+t*i),e}}function Ar(r,t){var e,i={},n={};for(e in r!==null&&typeof r=="object"||(r={}),t!==null&&typeof t=="object"||(t={}),t)e in r?i[e]=Y(r[e],t[e]):n[e]=t[e];return function(o){for(e in i)n[e]=i[e](o);return n}}function Y(r,t){var e,i,n=typeof t;return t==null||n==="boolean"?hr(t):(n==="number"?B:n==="string"?(e=Q(t))?(t=e,K):mr:t instanceof Q?K:t instanceof Date?kr:(i=t,!ArrayBuffer.isView(i)||i instanceof DataView?Array.isArray(t)?xr:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?Ar:B:Nr))(r,t)}function Sr(r,t){return r=+r,t=+t,function(e){return Math.round(r*(1-e)+t*e)}}function F(r,t){if((e=(r=t?r.toExponential(t-1):r.toExponential()).indexOf("e"))<0)return null;var e,i=r.slice(0,e);return[i.length>1?i[0]+i.slice(2):i,+r.slice(e+1)]}function $(r){return(r=F(Math.abs(r)))?r[1]:NaN}var or,jr=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function L(r){if(!(t=jr.exec(r)))throw new Error("invalid format: "+r);var t;return new R({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function R(r){this.fill=r.fill===void 0?" ":r.fill+"",this.align=r.align===void 0?">":r.align+"",this.sign=r.sign===void 0?"-":r.sign+"",this.symbol=r.symbol===void 0?"":r.symbol+"",this.zero=!!r.zero,this.width=r.width===void 0?void 0:+r.width,this.comma=!!r.comma,this.precision=r.precision===void 0?void 0:+r.precision,this.trim=!!r.trim,this.type=r.type===void 0?"":r.type+""}function W(r,t){var e=F(r,t);if(!e)return r+"";var i=e[0],n=e[1];return n<0?"0."+new Array(-n).join("0")+i:i.length>n+1?i.slice(0,n+1)+"."+i.slice(n+1):i+new Array(n-i.length+2).join("0")}L.prototype=R.prototype,R.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,0|this.width))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const _={"%":(r,t)=>(100*r).toFixed(t),b:r=>Math.round(r).toString(2),c:r=>r+"",d:function(r){return Math.abs(r=Math.round(r))>=1e21?r.toLocaleString("en").replace(/,/g,""):r.toString(10)},e:(r,t)=>r.toExponential(t),f:(r,t)=>r.toFixed(t),g:(r,t)=>r.toPrecision(t),o:r=>Math.round(r).toString(8),p:(r,t)=>W(100*r,t),r:W,s:function(r,t){var e=F(r,t);if(!e)return r+"";var i=e[0],n=e[1],o=n-(or=3*Math.max(-8,Math.min(8,Math.floor(n/3))))+1,a=i.length;return o===a?i:o>a?i+new Array(o-a+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+new Array(1-o).join("0")+F(r,Math.max(0,t+o-1))[0]},X:r=>Math.round(r).toString(16).toUpperCase(),x:r=>Math.round(r).toString(16)};function rr(r){return r}var X,ar,ur,tr=Array.prototype.map,er=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function zr(r){var t,e,i=r.grouping===void 0||r.thousands===void 0?rr:(t=tr.call(r.grouping,Number),e=r.thousands+"",function(c,f){for(var v=c.length,p=[],b=0,M=t[0],w=0;v>0&&M>0&&(w+M+1>f&&(M=Math.max(1,f-w)),p.push(c.substring(v-=M,v+M)),!((w+=M+1)>f));)M=t[b=(b+1)%t.length];return p.reverse().join(e)}),n=r.currency===void 0?"":r.currency[0]+"",o=r.currency===void 0?"":r.currency[1]+"",a=r.decimal===void 0?".":r.decimal+"",u=r.numerals===void 0?rr:function(c){return function(f){return f.replace(/[0-9]/g,function(v){return c[+v]})}}(tr.call(r.numerals,String)),s=r.percent===void 0?"%":r.percent+"",l=r.minus===void 0?"−":r.minus+"",g=r.nan===void 0?"NaN":r.nan+"";function m(c){var f=(c=L(c)).fill,v=c.align,p=c.sign,b=c.symbol,M=c.zero,w=c.width,O=c.comma,x=c.precision,Z=c.trim,d=c.type;d==="n"?(O=!0,d="g"):_[d]||(x===void 0&&(x=12),Z=!0,d="g"),(M||f==="0"&&v==="=")&&(M=!0,f="0",v="=");var cr=b==="$"?n:b==="#"&&/[boxX]/.test(d)?"0"+d.toLowerCase():"",sr=b==="$"?o:/[%p]/.test(d)?s:"",H=_[d],lr=/[defgprs%]/.test(d);function I(h){var j,J,T,k=cr,y=sr;if(d==="c")y=H(h)+y,h="";else{var P=(h=+h)<0||1/h<0;if(h=isNaN(h)?g:H(Math.abs(h),x),Z&&(h=function(z){r:for(var V,fr=z.length,A=1,S=-1;A<fr;++A)switch(z[A]){case".":S=V=A;break;case"0":S===0&&(S=A),V=A;break;default:if(!+z[A])break r;S>0&&(S=0)}return S>0?z.slice(0,S)+z.slice(V+1):z}(h)),P&&+h==0&&p!=="+"&&(P=!1),k=(P?p==="("?p:l:p==="-"||p==="("?"":p)+k,y=(d==="s"?er[8+or/3]:"")+y+(P&&p==="("?")":""),lr){for(j=-1,J=h.length;++j<J;)if(48>(T=h.charCodeAt(j))||T>57){y=(T===46?a+h.slice(j+1):h.slice(j))+y,h=h.slice(0,j);break}}}O&&!M&&(h=i(h,1/0));var q=k.length+h.length+y.length,N=q<w?new Array(w-q+1).join(f):"";switch(O&&M&&(h=i(N+h,N.length?w-y.length:1/0),N=""),v){case"<":h=k+h+y+N;break;case"=":h=k+N+h+y;break;case"^":h=N.slice(0,q=N.length>>1)+k+h+y+N.slice(q);break;default:h=N+k+h+y}return u(h)}return x=x===void 0?6:/[gprs]/.test(d)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x)),I.toString=function(){return c+""},I}return{format:m,formatPrefix:function(c,f){var v=m(((c=L(c)).type="f",c)),p=3*Math.max(-8,Math.min(8,Math.floor($(f)/3))),b=Math.pow(10,-p),M=er[8+p/3];return function(w){return v(b*w)+M}}}}function $r(r){return+r}X=zr({thousands:",",grouping:[3],currency:["$",""]}),ar=X.format,ur=X.formatPrefix;var ir=[0,1];function E(r){return r}function U(r,t){return(t-=r=+r)?function(i){return(i-r)/t}:(e=isNaN(t)?NaN:.5,function(){return e});var e}function Er(r,t,e){var i=r[0],n=r[1],o=t[0],a=t[1];return n<i?(i=U(n,i),o=e(a,o)):(i=U(i,n),o=e(o,a)),function(u){return o(i(u))}}function Pr(r,t,e){var i=Math.min(r.length,t.length)-1,n=new Array(i),o=new Array(i),a=-1;for(r[i]<r[0]&&(r=r.slice().reverse(),t=t.slice().reverse());++a<i;)n[a]=U(r[a],r[a+1]),o[a]=e(t[a],t[a+1]);return function(u){var s=Mr(r,u,1,i)-1;return o[s](n[s](u))}}function qr(r,t){return t.domain(r.domain()).range(r.range()).interpolate(r.interpolate()).clamp(r.clamp()).unknown(r.unknown())}function Cr(){var r,t,e,i,n,o,a=ir,u=ir,s=Y,l=E;function g(){var c,f,v,p=Math.min(a.length,u.length);return l!==E&&(c=a[0],f=a[p-1],c>f&&(v=c,c=f,f=v),l=function(b){return Math.max(c,Math.min(f,b))}),i=p>2?Pr:Er,n=o=null,m}function m(c){return c==null||isNaN(c=+c)?e:(n||(n=i(a.map(r),u,s)))(r(l(c)))}return m.invert=function(c){return l(t((o||(o=i(u,a.map(r),B)))(c)))},m.domain=function(c){return arguments.length?(a=Array.from(c,$r),g()):a.slice()},m.range=function(c){return arguments.length?(u=Array.from(c),g()):u.slice()},m.rangeRound=function(c){return u=Array.from(c),s=Sr,g()},m.clamp=function(c){return arguments.length?(l=!!c||E,g()):l!==E},m.interpolate=function(c){return arguments.length?(s=c,g()):s},m.unknown=function(c){return arguments.length?(e=c,m):e},function(c,f){return r=c,t=f,g()}}function Dr(){return Cr()(E,E)}function Fr(r,t,e,i){var n,o=wr(r,t,e);switch((i=L(i??",f")).type){case"s":var a=Math.max(Math.abs(r),Math.abs(t));return i.precision!=null||isNaN(n=function(u,s){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor($(s)/3)))-$(Math.abs(u)))}(o,a))||(i.precision=n),ur(i,a);case"":case"e":case"g":case"p":case"r":i.precision!=null||isNaN(n=function(u,s){return u=Math.abs(u),s=Math.abs(s)-u,Math.max(0,$(s)-$(u))+1}(o,Math.max(Math.abs(r),Math.abs(t))))||(i.precision=n-(i.type==="e"));break;case"f":case"%":i.precision!=null||isNaN(n=function(u){return Math.max(0,-$(Math.abs(u)))}(o))||(i.precision=n-2*(i.type==="%"))}return ar(i)}function Lr(r){var t=r.domain;return r.ticks=function(e){var i=t();return function(n,o,a){if(!((a=+a)>0))return[];if((n=+n)==(o=+o))return[n];const u=o<n,[s,l,g]=u?D(o,n,a):D(n,o,a);if(!(l>=s))return[];const m=l-s+1,c=new Array(m);if(u)if(g<0)for(let f=0;f<m;++f)c[f]=(l-f)/-g;else for(let f=0;f<m;++f)c[f]=(l-f)*g;else if(g<0)for(let f=0;f<m;++f)c[f]=(s+f)/-g;else for(let f=0;f<m;++f)c[f]=(s+f)*g;return c}(i[0],i[i.length-1],e??10)},r.tickFormat=function(e,i){var n=t();return Fr(n[0],n[n.length-1],e??10,i)},r.nice=function(e){e==null&&(e=10);var i,n,o=t(),a=0,u=o.length-1,s=o[a],l=o[u],g=10;for(l<s&&(n=s,s=l,l=n,n=a,a=u,u=n);g-- >0;){if((n=G(s,l,e))===i)return o[a]=s,o[u]=l,t(o);if(n>0)s=Math.floor(s/n)*n,l=Math.ceil(l/n)*n;else{if(!(n<0))break;s=Math.ceil(s*n)/n,l=Math.floor(l*n)/n}i=n}return r},r}function Or(){var r=Dr();return r.copy=function(){return qr(r,Or())},gr.apply(r,arguments),Lr(r)}export{qr as a,nr as b,Dr as c,Or as l,wr as t};
