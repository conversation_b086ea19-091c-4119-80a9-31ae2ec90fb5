import{l as n,f as c,a as m,t as r,b as i}from"./SpinnerAugment-BY2Lraps.js";import{h as p}from"./IconButtonAugment-B8y0FMb_.js";var w=c("<svg><!></svg>");function h(s,a){const e=n(a,["children","$$slots","$$events","$$legacy"]);var o=w();m(o,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...e}));var t=r(o);p(t,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M432 256a48 48 0 1 1-96 0 48 48 0 1 1 96 0m-160 0a48 48 0 1 1-96 0 48 48 0 1 1 96 0M64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96"/>',!0),i(s,o)}export{h as E};
