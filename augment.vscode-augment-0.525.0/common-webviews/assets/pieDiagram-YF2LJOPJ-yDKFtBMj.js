import{p as E}from"./chunk-TMUBEWPD-DfxHYFPc.js";import{T as y,O,aF as L,_ as m,g as N,s as P,a as V,b as G,o as I,p as _,l as F,c as q,E as H,I as J,a4 as K,e as Q,x as U,G as X}from"./AugmentMessage-Dd8zwzVO.js";import{p as Y}from"./gitGraph-YCYPL57B-CIVkQpNy.js";import{d as B}from"./arc-Bjmk69Yr.js";import{o as Z}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-BY2Lraps.js";import"./CalloutAugment-BPYQDfw6.js";import"./CardAugment-BaFOe6RO.js";import"./IconButtonAugment-B8y0FMb_.js";import"./index-Ci1AwCq1.js";import"./async-messaging-BnOo7nYC.js";import"./message-broker-BauNv3yh.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CAuiPHBF.js";import"./isObjectLike-DuRpH5zX.js";import"./BaseTextInput-C64uUToe.js";import"./index-CoHT-xzg.js";import"./diff-operations-TFiyZoZ6.js";import"./svelte-component-DfqKRK9G.js";import"./Filespan-7QsRDmJG.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BPm23rLE.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-B6RvTonN.js";import"./await-CoczQRp_.js";import"./OpenFileButton-DfoRAJ9f.js";import"./chat-model-context-C9JFkoqk.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-XI3B217g.js";import"./ra-diff-ops-model-3WeVBTrk.js";import"./TextAreaAugment-BkN7aH6v.js";import"./ButtonAugment-BoJU5mQc.js";import"./CollapseButtonAugment-CllkyJKm.js";import"./trash-can-CYj-Ewbo.js";import"./MaterialIcon-CKuUXxrb.js";import"./feedback-rating-BX5Icwas.js";import"./copy-DdR1jezc.js";import"./ellipsis-BVNflcFA.js";import"./LanguageIcon-BvE8QmB9.js";import"./chevron-down-CVLGkBkY.js";import"./index-ALhsmmIa.js";import"./augment-logo-DFXa-EF4.js";import"./pen-to-square-BKF2K8ly.js";import"./_baseUniq-Deams-7b.js";import"./_basePickBy-CHLhv2A5.js";import"./clone-Dy4Jp3mi.js";import"./init-g68aIKmP.js";function tt(t,r){return r<t?-1:r>t?1:r>=t?0:NaN}function et(t){return t}var rt=X.pie,R={sections:new Map,showData:!1},M=R.sections,z=R.showData,at=structuredClone(rt),W={getConfig:m(()=>structuredClone(at),"getConfig"),clear:m(()=>{M=new Map,z=R.showData,U()},"clear"),setDiagramTitle:_,getDiagramTitle:I,setAccTitle:G,getAccTitle:V,setAccDescription:P,getAccDescription:N,addSection:m(({label:t,value:r})=>{M.has(t)||(M.set(t,r),F.debug(`added new section: ${t}, with value: ${r}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{z=t},"setShowData"),getShowData:m(()=>z,"getShowData")},it=m((t,r)=>{E(t,r),r.setShowData(t.showData),t.sections.map(r.addSection)},"populateDb"),nt={parse:m(async t=>{const r=await Y("pie",t);F.debug(r),it(r,W)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),st=m(t=>{const r=[...t.entries()].map(s=>({label:s[0],value:s[1]})).sort((s,u)=>u.value-s.value);return function(){var s=et,u=tt,c=null,w=y(0),S=y(O),$=y(0);function a(e){var i,l,n,T,g,p=(e=L(e)).length,v=0,A=new Array(p),d=new Array(p),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/p,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<p;++i)(g=d[A[i]=i]=+s(e[i],i,e))>0&&(v+=g);for(u!=null?A.sort(function(x,D){return u(d[x],d[D])}):c!=null&&A.sort(function(x,D){return c(e[x],e[D])}),i=0,n=v?(C-p*b)/v:0;i<p;++i,f=T)l=A[i],T=f+((g=d[l])>0?g*n:0)+b,d[l]={data:e[l],index:i,value:g,startAngle:f,endAngle:T,padAngle:h};return d}return a.value=function(e){return arguments.length?(s=typeof e=="function"?e:y(+e),a):s},a.sortValues=function(e){return arguments.length?(u=e,c=null,a):u},a.sort=function(e){return arguments.length?(c=e,u=null,a):c},a.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),a):w},a.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),a):S},a.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),a):$},a}().value(s=>s.value)(r)},"createPieArcs"),re={parser:nt,db:W,renderer:{draw:m((t,r,s,u)=>{F.debug(`rendering pie chart
`+t);const c=u.db,w=q(),S=H(c.getConfig(),w.pie),$=18,a=450,e=a,i=J(r),l=i.append("g");l.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[T]=K(n.pieOuterStrokeWidth);T??(T=2);const g=S.textPosition,p=Math.min(e,a)/2-40,v=B().innerRadius(0).outerRadius(p),A=B().innerRadius(p*g).outerRadius(p*g);l.append("circle").attr("cx",0).attr("cy",0).attr("r",p+T/2).attr("class","pieOuterCircle");const d=c.getSections(),f=st(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=Z(C);l.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),l.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+A.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),l.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=l.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:j}=o.data;return c.getShowData()?`${k} [${j}]`:k});const D=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${D} 450`),Q(i,a,D,S.useMaxWidth)},"draw")},styles:ot};export{re as diagram};
