import{ag as O,an as B,E as D,ao as G,ap as H,O as I,L as J,aq as K,ar as N,as as Q,q as R,at as b,h as U,g as A,au as V,av as X,aw as Y}from"./SpinnerAugment-BY2Lraps.js";const g={tick:a=>requestAnimationFrame(a),now:()=>performance.now(),tasks:new Set};function T(){const a=g.now();g.tasks.forEach(t=>{t.c(a)||(g.tasks.delete(t),t.f())}),g.tasks.size!==0&&g.tick(T)}function k(a,t){N(()=>{a.dispatchEvent(new CustomEvent(t))})}function Z(a){if(a==="float")return"cssFloat";if(a==="offset")return"cssOffset";if(a.startsWith("--"))return a;const t=a.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(o=>o[0].toUpperCase()+o.slice(1)).join("")}function L(a){const t={},o=a.split(";");for(const e of o){const[c,n]=e.split(":");if(!c||n===void 0)break;t[Z(c.trim())]=n.trim()}return t}const z=a=>a;function nt(a,t,o,e){var c,n,r,f=!!(a&X),v=!!(a&Y),d=!!(a&K),s=f&&v?"both":f?"in":"out",$=t.inert,y=t.style.overflow;function h(){var u=V,x=O;U(null),A(null);try{return c??(c=o()(t,(e==null?void 0:e())??{},{direction:s}))}finally{U(u),A(x)}}var m={is_global:d,in(){var u;if(t.inert=$,!f)return r==null||r.abort(),void((u=r==null?void 0:r.reset)==null?void 0:u.call(r));v||(n==null||n.abort()),k(t,"introstart"),n=C(t,h(),r,1,()=>{k(t,"introend"),n==null||n.abort(),n=c=void 0,t.style.overflow=y})},out(u){if(!v)return u==null||u(),void(c=void 0);t.inert=!0,k(t,"outrostart"),r=C(t,h(),n,0,()=>{k(t,"outroend"),u==null||u()})},stop:()=>{n==null||n.abort(),r==null||r.abort()}},l=O;if((l.transitions??(l.transitions=[])).push(m),f&&B){var p=d;if(!p){for(var i=l.parent;i&&i.f&D;)for(;(i=i.parent)&&!(i.f&G););p=!i||!!(i.f&H)}p&&I(()=>{J(()=>m.in())})}}function C(a,t,o,e,c){var n=e===1;if(Q(t)){var r,f=!1;return R(()=>{if(!f){var p=t({direction:n?"in":"out"});r=C(a,p,o,e,c)}}),{abort:()=>{f=!0,r==null||r.abort()},deactivate:()=>r.deactivate(),reset:()=>r.reset(),t:()=>r.t()}}if(o==null||o.deactivate(),!(t!=null&&t.duration))return c(),{abort:b,deactivate:b,reset:b,t:()=>e};const{delay:v=0,css:d,tick:s,easing:$=z}=t;var y=[];if(n&&o===void 0&&(s&&s(0,1),d)){var h=L(d(0,1));y.push(h,h)}var m=()=>1-e,l=a.animate(y,{duration:v,fill:"forwards"});return l.onfinish=()=>{l.cancel();var p=(o==null?void 0:o.t())??1-e;o==null||o.abort();var i=e-p,u=t.duration*Math.abs(i),x=[];if(u>0){var E=!1;if(d)for(var q=Math.ceil(u/(1e3/60)),F=0;F<=q;F+=1){var M=p+i*$(F/q),W=L(d(M,1-M));x.push(W),E||(E=W.overflow==="hidden")}E&&(a.style.overflow="hidden"),m=()=>{var w=l.currentTime;return p+i*$(w/u)},s&&function(w){let _;g.tasks.size===0&&g.tick(T),new Promise(j=>{g.tasks.add(_={c:w,f:j})})}(()=>{if(l.playState!=="running")return!1;var w=m();return s(w,1-w),!0})}(l=a.animate(x,{duration:u,fill:"forwards"})).onfinish=()=>{m=()=>e,s==null||s(e,1-e),c()}},{abort:()=>{l&&(l.cancel(),l.effect=null,l.onfinish=b)},deactivate:()=>{c=b},reset:()=>{e===0&&(s==null||s(1,0))},t:()=>m()}}const tt=a=>a;function S(a){const t=a-1;return t*t*t+1}function P(a){const t=typeof a=="string"&&a.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[a,"px"]}function rt(a,{delay:t=0,duration:o=400,easing:e=tt}={}){const c=+getComputedStyle(a).opacity;return{delay:t,duration:o,easing:e,css:n=>"opacity: "+n*c}}function ot(a,{delay:t=0,duration:o=400,easing:e=S,x:c=0,y:n=0,opacity:r=0}={}){const f=getComputedStyle(a),v=+f.opacity,d=f.transform==="none"?"":f.transform,s=v*(1-r),[$,y]=P(c),[h,m]=P(n);return{delay:t,duration:o,easing:e,css:(l,p)=>`
			transform: ${d} translate(${(1-l)*$}${y}, ${(1-l)*h}${m});
			opacity: ${v-s*p}`}}function et(a,{delay:t=0,duration:o=400,easing:e=S,axis:c="y"}={}){const n=getComputedStyle(a),r=+n.opacity,f=c==="y"?"height":"width",v=parseFloat(n[f]),d=c==="y"?["top","bottom"]:["left","right"],s=d.map(i=>`${i[0].toUpperCase()}${i.slice(1)}`),$=parseFloat(n[`padding${s[0]}`]),y=parseFloat(n[`padding${s[1]}`]),h=parseFloat(n[`margin${s[0]}`]),m=parseFloat(n[`margin${s[1]}`]),l=parseFloat(n[`border${s[0]}Width`]),p=parseFloat(n[`border${s[1]}Width`]);return{delay:t,duration:o,easing:e,css:i=>`overflow: hidden;opacity: ${Math.min(20*i,1)*r};${f}: ${i*v}px;padding-${d[0]}: ${i*$}px;padding-${d[1]}: ${i*y}px;margin-${d[0]}: ${i*h}px;margin-${d[1]}: ${i*m}px;border-${d[0]}-width: ${i*l}px;border-${d[1]}-width: ${i*p}px;min-${f}: 0`}}function st(a,{delay:t=0,duration:o=400,easing:e=S,start:c=0,opacity:n=0}={}){const r=getComputedStyle(a),f=+r.opacity,v=r.transform==="none"?"":r.transform,d=1-c,s=f*(1-n);return{delay:t,duration:o,easing:e,css:($,y)=>`
			transform: ${v} scale(${1-d*y});
			opacity: ${f-s*y}
		`}}export{rt as a,st as b,ot as f,et as s,nt as t};
