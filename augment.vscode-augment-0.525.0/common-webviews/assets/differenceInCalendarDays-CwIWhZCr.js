function c(t){const e=Object.prototype.toString.call(t);return t instanceof Date||typeof t=="object"&&e==="[object Date]"?new t.constructor(+t):typeof t=="number"||e==="[object Number]"||typeof t=="string"||e==="[object String]"?new Date(t):new Date(NaN)}const g=6048e5,i=6e4,l=36e5;function r(t){const e=c(t);return e.setHours(0,0,0,0),e}function a(t){const e=c(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function b(t,e){const n=r(t),o=r(e),s=+n-a(n),u=+o-a(o);return Math.round((s-u)/864e5)}export{l as a,i as b,b as d,g as m,c as t};
