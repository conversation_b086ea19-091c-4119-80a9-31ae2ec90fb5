import{l as G,A as at,C as r,m,X as M,$ as st,D as lt,J as E,F as S,G as F,H as U,b as g,Y as c,_ as s,a3 as vt,a7 as tt,N as T,M as J,t as Y,I as nt,L as rt,K as O,Z as it,z as ht,ak as dt,a as ft,a1 as pt,am as mt,O as p,P as $,Q as gt,R as $t,W as yt}from"./SpinnerAugment-BY2Lraps.js";import{I as bt,b as l,a as wt}from"./IconButtonAugment-B8y0FMb_.js";import{T as xt,a as et}from"./CardAugment-BaFOe6RO.js";import{B as kt}from"./ButtonAugment-BoJU5mQc.js";import{B as Ct,b as Lt}from"./BaseTextInput-C64uUToe.js";var Rt=E("<!> <!> <!>",1),Tt=E('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function Mt(P,t){var a;const Q=G(t,["children","$$slots","$$events","$$legacy"]),H=G(Q,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);at(t,!1);const y=m(),b=m(),L=m();let w,x=r(t,"defaultColor",8),I=r(t,"tooltip",24,()=>{}),N=r(t,"stateVariant",24,()=>{}),W=r(t,"onClick",8),A=r(t,"tooltipDuration",8,1500),u=r(t,"icon",8,!1),V=r(t,"stickyColor",8,!0),X=r(t,"persistOnTooltipClose",8,!1),B=r(t,"tooltipNested",24,()=>{}),i=m("neutral"),k=m(x()),_=m(void 0),D=m((a=I())==null?void 0:a.neutral);async function K(o){var d;try{c(i,await W()(o)??"neutral")}catch(h){console.error(h),c(i,"failure")}c(D,(d=I())==null?void 0:d[s(i)]),clearTimeout(w),w=setTimeout(()=>{var h;(h=s(_))==null||h(),V()||c(i,"neutral")},A())}M(()=>(s(y),s(b),O(H)),()=>{c(y,H.variant),c(b,it(H,["variant"]))}),M(()=>(O(N()),s(i),s(y)),()=>{var o;c(L,((o=N())==null?void 0:o[s(i)])??s(y))}),M(()=>(s(i),O(x())),()=>{s(i)==="success"?c(k,"success"):s(i)==="failure"?c(k,"error"):c(k,x())}),st(),lt();var v=Tt(),R=Y(v);const n=vt(()=>(O(et),rt(()=>[et.Hover])));xt(R,{onOpenChange:function(o){var d;X()||o||(clearTimeout(w),w=void 0,c(D,(d=I())==null?void 0:d.neutral),V()||c(i,"neutral"))},get content(){return s(D)},get triggerOn(){return s(n)},get nested(){return B()},get requestClose(){return s(_)},set requestClose(o){c(_,o)},children:(o,d)=>{var h=S(),Z=F(h),ot=q=>{bt(q,tt(()=>s(b),{get color(){return s(k)},get variant(){return s(L)},$$events:{click:K,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,j)=>{var f=Rt(),C=F(f);T(C,t,"iconLeft",{},null);var z=J(C,2);T(z,t,"default",{},null);var ut=J(z,2);T(ut,t,"iconRight",{},null),g(e,f)},$$slots:{default:!0}}))},ct=q=>{kt(q,tt(()=>s(b),{get color(){return s(k)},get variant(){return s(L)},$$events:{click:K,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,j)=>{var f=S(),C=F(f);T(C,t,"default",{},null),g(e,f)},$$slots:{default:!0,iconLeft:(e,j)=>{var f=S(),C=F(f);T(C,t,"iconLeft",{},null),g(e,f)},iconRight:(e,j)=>{var f=S(),C=F(f);T(C,t,"iconRight",{},null),g(e,f)}}}))};U(Z,q=>{u()?q(ot):q(ct,!1)}),g(o,h)},$$slots:{default:!0},$$legacy:!0}),g(P,v),nt()}var Ot=E('<label class="c-text-area-label svelte-c1sr7w"> </label>'),At=E('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),_t=E("<textarea></textarea>"),qt=E('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function Nt(P,t){const Q=ht(t),H=G(t,["children","$$slots","$$events","$$legacy"]),y=G(H,["label","variant","size","color","resize","textInput","type","value","id"]);at(t,!1);const b=m(),L=m(),w=m();let x=r(t,"label",24,()=>{}),I=r(t,"variant",8,"surface"),N=r(t,"size",8,2),W=r(t,"color",24,()=>{}),A=r(t,"resize",8,"none"),u=r(t,"textInput",28,()=>{}),V=r(t,"type",8,"default"),X=r(t,"value",12,""),B=r(t,"id",24,()=>{});function i(){if(!u())return;u(u().style.height="auto",!0);const v=.8*window.innerHeight,R=Math.min(u().scrollHeight,v);u(u().style.height=`${R}px`,!0),u(u().style.overflowY=u().scrollHeight>v?"auto":"hidden",!0)}dt(()=>{if(u()){requestAnimationFrame(i);const v=()=>i();return window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)}}}),M(()=>O(B()),()=>{c(b,B()||`text-field-${Math.random().toString(36).substring(2,11)}`)}),M(()=>(s(L),s(w),O(y)),()=>{c(L,y.class),c(w,it(y,["class"]))}),st(),lt();var k=qt(),_=Y(k),D=v=>{var R=At(),n=Y(R),a=d=>{var h=Ot(),Z=Y(h);gt(()=>{$t(h,"for",s(b)),yt(Z,x())}),g(d,h)};U(n,d=>{x()&&d(a)});var o=J(n,2);T(o,t,"topRightAction",{},null),g(v,R)};U(_,v=>{O(x()),rt(()=>x()||Q.topRightAction)&&v(D)});var K=J(_,2);Ct(K,{get type(){return V()},get variant(){return I()},get size(){return N()},get color(){return W()},children:(v,R)=>{var n=_t();ft(n,a=>({id:s(b),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${s(L)}`,...s(w),[pt]:a}),[()=>({"c-textarea--resize-none":A()==="none","c-textarea--resize-both":A()==="both","c-textarea--resize-horizontal":A()==="horizontal","c-textarea--resize-vertical":A()==="vertical"})],"svelte-c1sr7w"),mt(n,a=>u(a),()=>u()),p(()=>Lt(n,X)),wt(n,a=>function(o){requestAnimationFrame(i);const d=()=>i();o.addEventListener("input",d);const h=new ResizeObserver(i);return h.observe(o),{destroy(){o.removeEventListener("input",d),h.disconnect()}}}(a)),p(()=>$("click",n,function(a){l.call(this,t,a)})),p(()=>$("focus",n,function(a){l.call(this,t,a)})),p(()=>$("keydown",n,function(a){l.call(this,t,a)})),p(()=>$("change",n,function(a){l.call(this,t,a)})),p(()=>$("input",n,function(a){l.call(this,t,a)})),p(()=>$("keyup",n,function(a){l.call(this,t,a)})),p(()=>$("blur",n,function(a){l.call(this,t,a)})),p(()=>$("select",n,function(a){l.call(this,t,a)})),p(()=>$("mouseup",n,function(a){l.call(this,t,a)})),g(v,n)},$$slots:{default:!0}}),g(P,k),nt()}export{Mt as S,Nt as T};
