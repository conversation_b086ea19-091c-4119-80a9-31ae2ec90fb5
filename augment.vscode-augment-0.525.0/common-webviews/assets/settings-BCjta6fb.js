var xa=Object.defineProperty;var Ma=(r,e,t)=>e in r?xa(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var Ze=(r,e,t)=>Ma(r,typeof e!="symbol"?e+"":e,t);import{aK as Aa,b5 as Ta,b6 as Na,f as bt,b as l,w as ht,aD as Pr,b7 as Ea,aA as Cr,A as st,C as w,D as nt,J as g,H as U,_ as n,L as f,t as h,M as v,a3 as xe,Q as fe,ab as At,W as ze,aF as ps,T as ce,V as N,P as Ct,I as rt,X as ve,Y as m,m as j,K as C,$ as dt,R as es,S as Ia,F as lt,G as $e,a4 as et,a2 as Et,z as Ra,l as rs,Z as za,a as ws,a1 as La,N as Mt,al as Ir,a7 as Oa,b8 as Zr,ay as tr,v as Js,u as vs,b9 as Fr,aE as pr,a5 as Ns,B as Rr,ak as Pa,b0 as jr,a6 as aa,ba as Za,az as Fa}from"./SpinnerAugment-BY2Lraps.js";import"./design-system-init-DkEuonq_.js";import{W as Je,d as kt,e as pt,I as as,b as ja,c as ut,i as Bt,a as Da,h as Hs,f as Dr,g as Ua,H as vr}from"./IconButtonAugment-B8y0FMb_.js";import{R as Ur,M as Va}from"./message-broker-BauNv3yh.js";import{G as qa,S as Ha,b as Ba,N as Ga,L as Ja,c as gt,M as ds,D as Wa,F as Ka,f as na,R as Ya,d as Vr,T as ia,e as Qa,C as Xa,P as mr,g as en,h as tn,i as sn,A as rn,j as an,k as nn}from"./trash-can-CYj-Ewbo.js";import{Q as gr,a2 as Zt,O as Ge,i as fr,t as wr,T as ts,D as tt,a3 as on,C as oa,E as la,a4 as Ws,f as yr,A as qr,g as ln,h as dn,R as cn}from"./index-Ci1AwCq1.js";import{G as un,D as Ks,C as hn,P as gs,B as br,g as da,T as $r,a as ca,S as Sr,c as pn,s as vn}from"./download-nkXMXEb3.js";import{o as Qs}from"./keypress-DD1aQVr0.js";import{V as ua}from"./VSCodeCodicon-Bvj-edDt.js";import{A as mn}from"./async-messaging-BnOo7nYC.js";import{c as kr}from"./svelte-component-DfqKRK9G.js";import{k as gn,C as ha,a as fn,T as Xs}from"./CollapseButtonAugment-CllkyJKm.js";import{D as yn}from"./Drawer-osepH3Vw.js";import{b as pa,T as jt,a as hs,p as _n}from"./CardAugment-BaFOe6RO.js";import{B as Qe}from"./ButtonAugment-BoJU5mQc.js";import{C as Es}from"./CalloutAugment-BPYQDfw6.js";import{E as Cn}from"./ellipsis-BVNflcFA.js";import{P as wn}from"./pen-to-square-BKF2K8ly.js";import{T as va,S as bn}from"./TextAreaAugment-BkN7aH6v.js";import{C as $n}from"./copy-DdR1jezc.js";import{C as zr}from"./chevron-down-CVLGkBkY.js";import{M as Sn}from"./index-BPm23rLE.js";import{M as kn}from"./MarkdownEditor-DyMkuzW7.js";import{R as xn}from"./RulesModeSelector-e941xJbL.js";import{M as ma}from"./ModalAugment-BvhJgadP.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CAuiPHBF.js";import"./isObjectLike-DuRpH5zX.js";import"./BaseTextInput-C64uUToe.js";import"./index-ALhsmmIa.js";import"./index-CoHT-xzg.js";const Mn=[];function Hr(r,e=!1){return er(r,new Map,"",Mn)}function er(r,e,t,s,a=null){if(typeof r=="object"&&r!==null){var o=e.get(r);if(o!==void 0)return o;if(r instanceof Map)return new Map(r);if(r instanceof Set)return new Set(r);if(Aa(r)){var i=Array(r.length);e.set(r,i),a!==null&&e.set(a,i);for(var d=0;d<r.length;d+=1){var c=r[d];d in r&&(i[d]=er(c,e,t,s))}return i}if(Ta(r)===Na){for(var u in i={},e.set(r,i),a!==null&&e.set(a,i),r)i[u]=er(r[u],e,t,s);return i}if(r instanceof Date)return structuredClone(r);if(typeof r.toJSON=="function")return er(r.toJSON(),e,t,s,r)}if(r instanceof EventTarget)return r;try{return structuredClone(r)}catch{return r}}const ks={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class An{constructor(e,t=ks){Ze(this,"timerId",null);Ze(this,"currentMS");Ze(this,"step",0);Ze(this,"params");this.callback=e;const s={...t};s.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),s.maxMS=ks.maxMS),s.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),s.initialMS=ks.initialMS),s.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),s.mult=ks.mult),s.maxSteps!==void 0&&s.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),s.maxSteps=ks.maxSteps),this.params=s,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}var Tn=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z" fill="currentColor"></path><path d="M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z" fill="currentColor"></path></svg>');function Nn(r){var e=Tn();l(r,e)}var En=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z" fill="currentColor"></path></svg>');function In(r){var e=En();l(r,e)}var Rn=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>');function qt(r){var e=Rn();l(r,e)}var Ve,xr,zn=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z" fill="currentColor"></path></svg>');function Ln(r){var e=zn();l(r,e)}class fs{constructor(e){Ze(this,"configs",ht([]));Ze(this,"pollingManager");Ze(this,"_enableDebugFeatures",ht(!1));Ze(this,"_settingsComponentSupported",ht({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));Ze(this,"_enableAgentMode",ht(!1));Ze(this,"_enableAgentSwarmMode",ht(!1));Ze(this,"_enableNativeRemoteMcp",ht(!0));Ze(this,"_hasEverUsedRemoteAgent",ht(!1));Ze(this,"_enableInitialOrientation",ht(!1));Ze(this,"_userTier",ht("unknown"));Ze(this,"_userEmail",ht(void 0));Ze(this,"_guidelines",ht({}));this._host=e,this.pollingManager=new An(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,s=e.oauthUrl;if(e.identifier.hostName===gr.remoteToolHost){let a=e.identifier.toolId;switch(typeof a=="string"&&/^\d+$/.test(a)&&(a=Number(a)),a){case Zt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:un,requiresAuthentication:t,authUrl:s};case Zt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Ja,requiresAuthentication:t,authUrl:s};case Zt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:In,requiresAuthentication:t,authUrl:s};case Zt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Ga,requiresAuthentication:t,authUrl:s};case Zt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:Nn,requiresAuthentication:t,authUrl:s};case Zt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:Ba,requiresAuthentication:t,authUrl:s};case Zt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Ha,requiresAuthentication:t,authUrl:s};case Zt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:qa,requiresAuthentication:t,authUrl:s};case Zt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled RemoteToolId: ${a}`)}}else if(e.identifier.hostName===gr.localToolHost){const a=e.identifier.toolId;switch(a){case Ge.readFile:case Ge.editFile:case Ge.saveFile:case Ge.launchProcess:case Ge.killProcess:case Ge.readProcess:case Ge.writeProcess:case Ge.listProcesses:case Ge.waitProcess:case Ge.openBrowser:case Ge.clarify:case Ge.onboardingSubAgent:case Ge.strReplaceEditor:case Ge.remember:case Ge.diagnostics:case Ge.webFetch:case Ge.setupScript:case Ge.readTerminal:case Ge.gitCommitRetrieval:case Ge.memoryRetrieval:case Ge.startWorkerAgent:case Ge.readWorkerState:case Ge.waitForWorkerAgent:case Ge.sendInstructionToWorkerAgent:case Ge.stopWorkerAgent:case Ge.deleteWorkerAgent:case Ge.readWorkerAgentEdits:case Ge.applyWorkerAgentEdits:case Ge.LocalSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:qt,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled LocalToolType: ${a}`)}}else if(e.identifier.hostName===gr.sidecarToolHost){const a=e.identifier.toolId;switch(a){case gt.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:ds,requiresAuthentication:t,authUrl:s};case gt.shell:return{displayName:"Shell",description:"Shell",icon:ds,requiresAuthentication:t,authUrl:s};case gt.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:ds,requiresAuthentication:t,authUrl:s};case gt.view:return{displayName:"File View",description:"File Viewer",icon:ds,requiresAuthentication:t,authUrl:s};case gt.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:ds,requiresAuthentication:t,authUrl:s};case gt.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Ln,requiresAuthentication:t,authUrl:s};case gt.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:qt,requiresAuthentication:t,authUrl:s};case gt.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Ka,requiresAuthentication:t,authUrl:s};case gt.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:qt,requiresAuthentication:t,authUrl:s};case gt.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:qt,requiresAuthentication:t,authUrl:s};case gt.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:qt,requiresAuthentication:t,authUrl:s};case gt.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:qt,requiresAuthentication:t,authUrl:s};case gt.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:qt,requiresAuthentication:t,authUrl:s};case gt.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:qt,requiresAuthentication:t,authUrl:s};case gt.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Wa,requiresAuthentication:t,authUrl:s};case gt.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:ds,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled SidecarToolType: ${a}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:s}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case Je.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(t.data.enableAgentSwarmMode),t.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(t.data.hasEverUsedRemoteAgent),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.userEmail!==void 0&&this._userEmail.set(t.data.userEmail),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),t.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(t.data.enableNativeRemoteMcp),!0;case Je.toolConfigDefinitionsResponse:return this.configs.update(s=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(a=>{const o=s.find(i=>i.name===a.name);return o?{...o,displayName:a.displayName,description:a.description,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,isConfigured:a.isConfigured,toolApprovalConfig:a.toolApprovalConfig}:a})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(s=>{const a=this.transformToolDisplay(s),o=t.find(d=>d.name===s.definition.name),i=(o==null?void 0:o.isConfigured)??!a.requiresAuthentication;return{config:(o==null?void 0:o.config)??{},configString:JSON.stringify((o==null?void 0:o.config)??{},null,2),isConfigured:i,name:s.definition.name.toString(),displayName:a.displayName,description:a.description,identifier:s.identifier,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:s.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return Pr(this.configs,e=>{const t=e.filter(a=>this.isDisplayableTool(a)),s=new Map;for(const a of t)s.set(a.displayName,a);return Array.from(s.values()).sort((a,o)=>{const i={GitHub:1,Linear:2,Notion:3},d=Number.MAX_SAFE_INTEGER,c=i[a.displayName]||d,u=i[o.displayName]||d;return c<d&&u<d||c===d&&u===d?c!==u?c-u:a.displayName.localeCompare(o.displayName):c-u})})}getPretendNativeToolDefs(){return Pr(this.configs,e=>this.getEnableNativeRemoteMcp()?na(e):[])}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:Je.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:Je.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"?this._enableNativeRemoteMcp:Ea(!1)}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getUserEmail(){return this._userEmail}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}updateToolApprovalConfig(e,t){this.configs.update(s=>s.map(a=>a.identifier.toolId===e.toolId&&a.identifier.hostName===e.hostName?{...a,toolApprovalConfig:t}:a))}getSettingsComponentSupported(){return this._settingsComponentSupported}}Ze(fs,"key","toolConfigModel");(function(r){r.assertEqual=e=>e,r.assertIs=function(e){},r.assertNever=function(e){throw new Error},r.arrayToEnum=e=>{const t={};for(const s of e)t[s]=s;return t},r.getValidEnumValues=e=>{const t=r.objectKeys(e).filter(a=>typeof e[e[a]]!="number"),s={};for(const a of t)s[a]=e[a];return r.objectValues(s)},r.objectValues=e=>r.objectKeys(e).map(function(t){return e[t]}),r.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.push(s);return t},r.find=(e,t)=>{for(const s of e)if(t(s))return s},r.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,r.joinValues=function(e,t=" | "){return e.map(s=>typeof s=="string"?`'${s}'`:s).join(t)},r.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(Ve||(Ve={})),function(r){r.mergeShapes=(e,t)=>({...e,...t})}(xr||(xr={}));const B=Ve.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Ft=r=>{switch(typeof r){case"undefined":return B.undefined;case"string":return B.string;case"number":return isNaN(r)?B.nan:B.number;case"boolean":return B.boolean;case"function":return B.function;case"bigint":return B.bigint;case"symbol":return B.symbol;case"object":return Array.isArray(r)?B.array:r===null?B.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?B.promise:typeof Map<"u"&&r instanceof Map?B.map:typeof Set<"u"&&r instanceof Set?B.set:typeof Date<"u"&&r instanceof Date?B.date:B.object;default:return B.unknown}},S=Ve.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class wt extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(o){return o.message},s={_errors:[]},a=o=>{for(const i of o.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let d=s,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(d[u]=d[u]||{_errors:[]},d[u]._errors.push(t(i))):d[u]=d[u]||{_errors:[]},d=d[u],c++}}};return a(this),s}static assert(e){if(!(e instanceof wt))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Ve.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):s.push(e(a));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}wt.create=r=>new wt(r);const ys=(r,e)=>{let t;switch(r.code){case S.invalid_type:t=r.received===B.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case S.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,Ve.jsonStringifyReplacer)}`;break;case S.unrecognized_keys:t=`Unrecognized key(s) in object: ${Ve.joinValues(r.keys,", ")}`;break;case S.invalid_union:t="Invalid input";break;case S.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${Ve.joinValues(r.options)}`;break;case S.invalid_enum_value:t=`Invalid enum value. Expected ${Ve.joinValues(r.options)}, received '${r.received}'`;break;case S.invalid_arguments:t="Invalid function arguments";break;case S.invalid_return_type:t="Invalid function return type";break;case S.invalid_date:t="Invalid date";break;case S.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:Ve.assertNever(r.validation):t=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case S.too_small:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case S.too_big:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case S.custom:t="Invalid input";break;case S.invalid_intersection_types:t="Intersection results could not be merged";break;case S.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case S.not_finite:t="Number must be finite";break;default:t=e.defaultError,Ve.assertNever(r)}return{message:t}};let ga=ys;function sr(){return ga}const rr=r=>{const{data:e,path:t,errorMaps:s,issueData:a}=r,o=[...t,...a.path||[]],i={...a,path:o};if(a.message!==void 0)return{...a,path:o,message:a.message};let d="";const c=s.filter(u=>!!u).slice().reverse();for(const u of c)d=u(i,{data:e,defaultError:d}).message;return{...a,path:o,message:d}};function D(r,e){const t=sr(),s=rr({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===ys?void 0:ys].filter(a=>!!a)});r.common.issues.push(s)}class yt{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const a of t){if(a.status==="aborted")return Ce;a.status==="dirty"&&e.dirty(),s.push(a.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const a of t){const o=await a.key,i=await a.value;s.push({key:o,value:i})}return yt.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const a of t){const{key:o,value:i}=a;if(o.status==="aborted"||i.status==="aborted")return Ce;o.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),o.value==="__proto__"||i.value===void 0&&!a.alwaysSet||(s[o.value]=i.value)}return{status:e.value,value:s}}}const Ce=Object.freeze({status:"aborted"}),ar=r=>({status:"dirty",value:r}),_t=r=>({status:"valid",value:r}),Mr=r=>r.status==="aborted",Ar=r=>r.status==="dirty",ns=r=>r.status==="valid",Is=r=>typeof Promise<"u"&&r instanceof Promise;function nr(r,e,t,s){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(r)}function fa(r,e,t,s,a){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}var ie,Ms,As;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(ie||(ie={}));class Ot{constructor(e,t,s,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=a}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Br=(r,e)=>{if(ns(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new wt(r.common.issues);return this._error=t,this._error}}};function Ie(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:a}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(o,i)=>{var d,c;const{message:u}=r;return o.code==="invalid_enum_value"?{message:u??i.defaultError}:i.data===void 0?{message:(d=u??s)!==null&&d!==void 0?d:i.defaultError}:o.code!=="invalid_type"?{message:i.defaultError}:{message:(c=u??t)!==null&&c!==void 0?c:i.defaultError}},description:a}}class Le{get description(){return this._def.description}_getType(e){return Ft(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new yt,ctx:{common:e.parent.common,data:e.data,parsedType:Ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Is(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;const a={common:{issues:[],async:(s=t==null?void 0:t.async)!==null&&s!==void 0&&s,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)},o=this._parseSync({data:e,path:a.path,parent:a});return Br(a,o)}"~validate"(e){var t,s;const a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)};if(!this["~standard"].async)try{const o=this._parseSync({data:e,path:[],parent:a});return ns(o)?{value:o.value}:{issues:a.common.issues}}catch(o){!((s=(t=o==null?void 0:o.message)===null||t===void 0?void 0:t.toLowerCase())===null||s===void 0)&&s.includes("encountered")&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:a}).then(o=>ns(o)?{value:o.value}:{issues:a.common.issues})}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)},a=this._parse({data:e,path:s.path,parent:s}),o=await(Is(a)?a:Promise.resolve(a));return Br(s,o)}refine(e,t){const s=a=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(a):t;return this._refinement((a,o)=>{const i=e(a),d=()=>o.addIssue({code:S.custom,...s(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(d(),!1)):!!i||(d(),!1)})}refinement(e,t){return this._refinement((s,a)=>!!e(s)||(a.addIssue(typeof t=="function"?t(s,a):t),!1))}_refinement(e){return new Nt({schema:this,typeName:_e.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return zt.create(this,this._def)}nullable(){return Yt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return It.create(this)}promise(){return Cs.create(this,this._def)}or(e){return Os.create([this,e],this._def)}and(e){return Ps.create(this,e,this._def)}transform(e){return new Nt({...Ie(this._def),schema:this,typeName:_e.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Ds({...Ie(this._def),innerType:this,defaultValue:t,typeName:_e.ZodDefault})}brand(){return new Lr({typeName:_e.ZodBranded,type:this,...Ie(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Us({...Ie(this._def),innerType:this,catchValue:t,typeName:_e.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Bs.create(this,e)}readonly(){return Vs.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const On=/^c[^\s-]{8,}$/i,Pn=/^[0-9a-z]+$/,Zn=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Fn=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,jn=/^[a-z0-9_-]{21}$/i,Dn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Un=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Vn=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let _r;const qn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Hn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Bn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Gn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Jn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Wn=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ya="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Kn=new RegExp(`^${ya}$`);function _a(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Ca(r){let e=`${ya}T${_a(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Yn(r,e){if(!Dn.test(r))return!1;try{const[t]=r.split("."),s=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),a=JSON.parse(atob(s));return typeof a=="object"&&a!==null&&!(!a.typ||!a.alg)&&(!e||a.alg===e)}catch{return!1}}function Qn(r,e){return!(e!=="v4"&&e||!Hn.test(r))||!(e!=="v6"&&e||!Gn.test(r))}class Tt extends Le{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==B.string){const i=this._getOrReturnCtx(e);return D(i,{code:S.invalid_type,expected:B.string,received:i.parsedType}),Ce}const t=new yt;let s;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:S.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:S.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const d=e.data.length>i.value,c=e.data.length<i.value;(d||c)&&(s=this._getOrReturnCtx(e,s),d?D(s,{code:S.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&D(s,{code:S.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Vn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"email",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")_r||(_r=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),_r.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"emoji",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Fn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"uuid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")jn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"nanoid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")On.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"cuid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")Pn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"cuid2",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Zn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"ulid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),D(s,{validation:"url",code:S.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"regex",code:S.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),D(s,{code:S.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),D(s,{code:S.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),D(s,{code:S.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?Ca(i).test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{code:S.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?Kn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{code:S.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${_a(i)}$`).test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{code:S.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Un.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"duration",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(a=e.data,((o=i.version)!=="v4"&&o||!qn.test(a))&&(o!=="v6"&&o||!Bn.test(a))&&(s=this._getOrReturnCtx(e,s),D(s,{validation:"ip",code:S.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Yn(e.data,i.alg)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"jwt",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Qn(e.data,i.version)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"cidr",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?Jn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"base64",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Wn.test(e.data)||(s=this._getOrReturnCtx(e,s),D(s,{validation:"base64url",code:S.invalid_string,message:i.message}),t.dirty()):Ve.assertNever(i);var a,o;return{status:t.value,value:e.data}}_regex(e,t,s){return this.refinement(a=>e.test(a),{validation:t,code:S.invalid_string,...ie.errToObj(s)})}_addCheck(e){return new Tt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...ie.errToObj(e)})}url(e){return this._addCheck({kind:"url",...ie.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...ie.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...ie.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...ie.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...ie.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...ie.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...ie.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...ie.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...ie.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...ie.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...ie.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...ie.errToObj(e)})}datetime(e){var t,s;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(s=e==null?void 0:e.local)!==null&&s!==void 0&&s,...ie.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...ie.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...ie.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...ie.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...ie.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...ie.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...ie.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...ie.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...ie.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...ie.errToObj(t)})}nonempty(e){return this.min(1,ie.errToObj(e))}trim(){return new Tt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Tt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Tt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Xn(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,a=t>s?t:s;return parseInt(r.toFixed(a).replace(".",""))%parseInt(e.toFixed(a).replace(".",""))/Math.pow(10,a)}Tt.create=r=>{var e;return new Tt({checks:[],typeName:_e.ZodString,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Ie(r)})};class Jt extends Le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==B.number){const a=this._getOrReturnCtx(e);return D(a,{code:S.invalid_type,expected:B.number,received:a.parsedType}),Ce}let t;const s=new yt;for(const a of this._def.checks)a.kind==="int"?Ve.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),D(t,{code:S.invalid_type,expected:"integer",received:"float",message:a.message}),s.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:S.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:S.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="multipleOf"?Xn(e.data,a.value)!==0&&(t=this._getOrReturnCtx(e,t),D(t,{code:S.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),D(t,{code:S.not_finite,message:a.message}),s.dirty()):Ve.assertNever(a);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,ie.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ie.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ie.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ie.toString(t))}setLimit(e,t,s,a){return new Jt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ie.toString(a)}]})}_addCheck(e){return new Jt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:ie.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:ie.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:ie.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:ie.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:ie.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ie.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:ie.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:ie.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:ie.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&Ve.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Jt.create=r=>new Jt({checks:[],typeName:_e.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...Ie(r)});class Wt extends Le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==B.bigint)return this._getInvalidInput(e);let t;const s=new yt;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:S.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),D(t,{code:S.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),D(t,{code:S.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):Ve.assertNever(a);return{status:s.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.bigint,received:t.parsedType}),Ce}gte(e,t){return this.setLimit("min",e,!0,ie.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ie.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ie.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ie.toString(t))}setLimit(e,t,s,a){return new Wt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ie.toString(a)}]})}_addCheck(e){return new Wt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:ie.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:ie.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:ie.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:ie.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ie.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Wt.create=r=>{var e;return new Wt({checks:[],typeName:_e.ZodBigInt,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Ie(r)})};class Rs extends Le{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==B.boolean){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.boolean,received:t.parsedType}),Ce}return _t(e.data)}}Rs.create=r=>new Rs({typeName:_e.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...Ie(r)});class is extends Le{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==B.date){const a=this._getOrReturnCtx(e);return D(a,{code:S.invalid_type,expected:B.date,received:a.parsedType}),Ce}if(isNaN(e.data.getTime()))return D(this._getOrReturnCtx(e),{code:S.invalid_date}),Ce;const t=new yt;let s;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:S.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(s=this._getOrReturnCtx(e,s),D(s,{code:S.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):Ve.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new is({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:ie.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:ie.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}is.create=r=>new is({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:_e.ZodDate,...Ie(r)});class ir extends Le{_parse(e){if(this._getType(e)!==B.symbol){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.symbol,received:t.parsedType}),Ce}return _t(e.data)}}ir.create=r=>new ir({typeName:_e.ZodSymbol,...Ie(r)});class zs extends Le{_parse(e){if(this._getType(e)!==B.undefined){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.undefined,received:t.parsedType}),Ce}return _t(e.data)}}zs.create=r=>new zs({typeName:_e.ZodUndefined,...Ie(r)});class Ls extends Le{_parse(e){if(this._getType(e)!==B.null){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.null,received:t.parsedType}),Ce}return _t(e.data)}}Ls.create=r=>new Ls({typeName:_e.ZodNull,...Ie(r)});class _s extends Le{constructor(){super(...arguments),this._any=!0}_parse(e){return _t(e.data)}}_s.create=r=>new _s({typeName:_e.ZodAny,...Ie(r)});class ss extends Le{constructor(){super(...arguments),this._unknown=!0}_parse(e){return _t(e.data)}}ss.create=r=>new ss({typeName:_e.ZodUnknown,...Ie(r)});class Ut extends Le{_parse(e){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.never,received:t.parsedType}),Ce}}Ut.create=r=>new Ut({typeName:_e.ZodNever,...Ie(r)});class or extends Le{_parse(e){if(this._getType(e)!==B.undefined){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.void,received:t.parsedType}),Ce}return _t(e.data)}}or.create=r=>new or({typeName:_e.ZodVoid,...Ie(r)});class It extends Le{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),a=this._def;if(t.parsedType!==B.array)return D(t,{code:S.invalid_type,expected:B.array,received:t.parsedType}),Ce;if(a.exactLength!==null){const i=t.data.length>a.exactLength.value,d=t.data.length<a.exactLength.value;(i||d)&&(D(t,{code:i?S.too_big:S.too_small,minimum:d?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),s.dirty())}if(a.minLength!==null&&t.data.length<a.minLength.value&&(D(t,{code:S.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),s.dirty()),a.maxLength!==null&&t.data.length>a.maxLength.value&&(D(t,{code:S.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,d)=>a.type._parseAsync(new Ot(t,i,t.path,d)))).then(i=>yt.mergeArray(s,i));const o=[...t.data].map((i,d)=>a.type._parseSync(new Ot(t,i,t.path,d)));return yt.mergeArray(s,o)}get element(){return this._def.type}min(e,t){return new It({...this._def,minLength:{value:e,message:ie.toString(t)}})}max(e,t){return new It({...this._def,maxLength:{value:e,message:ie.toString(t)}})}length(e,t){return new It({...this._def,exactLength:{value:e,message:ie.toString(t)}})}nonempty(e){return this.min(1,e)}}function cs(r){if(r instanceof Xe){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=zt.create(cs(s))}return new Xe({...r._def,shape:()=>e})}return r instanceof It?new It({...r._def,type:cs(r.element)}):r instanceof zt?zt.create(cs(r.unwrap())):r instanceof Yt?Yt.create(cs(r.unwrap())):r instanceof Pt?Pt.create(r.items.map(e=>cs(e))):r}It.create=(r,e)=>new It({type:r,minLength:null,maxLength:null,exactLength:null,typeName:_e.ZodArray,...Ie(e)});class Xe extends Le{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=Ve.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==B.object){const c=this._getOrReturnCtx(e);return D(c,{code:S.invalid_type,expected:B.object,received:c.parsedType}),Ce}const{status:t,ctx:s}=this._processInputParams(e),{shape:a,keys:o}=this._getCached(),i=[];if(!(this._def.catchall instanceof Ut&&this._def.unknownKeys==="strip"))for(const c in s.data)o.includes(c)||i.push(c);const d=[];for(const c of o){const u=a[c],p=s.data[c];d.push({key:{status:"valid",value:c},value:u._parse(new Ot(s,p,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof Ut){const c=this._def.unknownKeys;if(c==="passthrough")for(const u of i)d.push({key:{status:"valid",value:u},value:{status:"valid",value:s.data[u]}});else if(c==="strict")i.length>0&&(D(s,{code:S.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const u of i){const p=s.data[u];d.push({key:{status:"valid",value:u},value:c._parse(new Ot(s,p,s.path,u)),alwaysSet:u in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const u of d){const p=await u.key,oe=await u.value;c.push({key:p,value:oe,alwaysSet:u.alwaysSet})}return c}).then(c=>yt.mergeObjectSync(t,c)):yt.mergeObjectSync(t,d)}get shape(){return this._def.shape()}strict(e){return ie.errToObj,new Xe({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var a,o,i,d;const c=(i=(o=(a=this._def).errorMap)===null||o===void 0?void 0:o.call(a,t,s).message)!==null&&i!==void 0?i:s.defaultError;return t.code==="unrecognized_keys"?{message:(d=ie.errToObj(e).message)!==null&&d!==void 0?d:c}:{message:c}}}:{}})}strip(){return new Xe({...this._def,unknownKeys:"strip"})}passthrough(){return new Xe({...this._def,unknownKeys:"passthrough"})}extend(e){return new Xe({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Xe({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:_e.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Xe({...this._def,catchall:e})}pick(e){const t={};return Ve.objectKeys(e).forEach(s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])}),new Xe({...this._def,shape:()=>t})}omit(e){const t={};return Ve.objectKeys(this.shape).forEach(s=>{e[s]||(t[s]=this.shape[s])}),new Xe({...this._def,shape:()=>t})}deepPartial(){return cs(this)}partial(e){const t={};return Ve.objectKeys(this.shape).forEach(s=>{const a=this.shape[s];e&&!e[s]?t[s]=a:t[s]=a.optional()}),new Xe({...this._def,shape:()=>t})}required(e){const t={};return Ve.objectKeys(this.shape).forEach(s=>{if(e&&!e[s])t[s]=this.shape[s];else{let a=this.shape[s];for(;a instanceof zt;)a=a._def.innerType;t[s]=a}}),new Xe({...this._def,shape:()=>t})}keyof(){return wa(Ve.objectKeys(this.shape))}}Xe.create=(r,e)=>new Xe({shape:()=>r,unknownKeys:"strip",catchall:Ut.create(),typeName:_e.ZodObject,...Ie(e)}),Xe.strictCreate=(r,e)=>new Xe({shape:()=>r,unknownKeys:"strict",catchall:Ut.create(),typeName:_e.ZodObject,...Ie(e)}),Xe.lazycreate=(r,e)=>new Xe({shape:r,unknownKeys:"strip",catchall:Ut.create(),typeName:_e.ZodObject,...Ie(e)});class Os extends Le{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;if(t.common.async)return Promise.all(s.map(async a=>{const o={...t,common:{...t.common,issues:[]},parent:null};return{result:await a._parseAsync({data:t.data,path:t.path,parent:o}),ctx:o}})).then(function(a){for(const i of a)if(i.result.status==="valid")return i.result;for(const i of a)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const o=a.map(i=>new wt(i.ctx.common.issues));return D(t,{code:S.invalid_union,unionErrors:o}),Ce});{let a;const o=[];for(const d of s){const c={...t,common:{...t.common,issues:[]},parent:null},u=d._parseSync({data:t.data,path:t.path,parent:c});if(u.status==="valid")return u;u.status!=="dirty"||a||(a={result:u,ctx:c}),c.common.issues.length&&o.push(c.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const i=o.map(d=>new wt(d));return D(t,{code:S.invalid_union,unionErrors:i}),Ce}}get options(){return this._def.options}}Os.create=(r,e)=>new Os({options:r,typeName:_e.ZodUnion,...Ie(e)});const Ht=r=>r instanceof Zs?Ht(r.schema):r instanceof Nt?Ht(r.innerType()):r instanceof Fs?[r.value]:r instanceof Kt?r.options:r instanceof js?Ve.objectValues(r.enum):r instanceof Ds?Ht(r._def.innerType):r instanceof zs?[void 0]:r instanceof Ls?[null]:r instanceof zt?[void 0,...Ht(r.unwrap())]:r instanceof Yt?[null,...Ht(r.unwrap())]:r instanceof Lr||r instanceof Vs?Ht(r.unwrap()):r instanceof Us?Ht(r._def.innerType):[];class cr extends Le{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.object)return D(t,{code:S.invalid_type,expected:B.object,received:t.parsedType}),Ce;const s=this.discriminator,a=t.data[s],o=this.optionsMap.get(a);return o?t.common.async?o._parseAsync({data:t.data,path:t.path,parent:t}):o._parseSync({data:t.data,path:t.path,parent:t}):(D(t,{code:S.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),Ce)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){const a=new Map;for(const o of t){const i=Ht(o.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const d of i){if(a.has(d))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(d)}`);a.set(d,o)}}return new cr({typeName:_e.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...Ie(s)})}}function Tr(r,e){const t=Ft(r),s=Ft(e);if(r===e)return{valid:!0,data:r};if(t===B.object&&s===B.object){const a=Ve.objectKeys(e),o=Ve.objectKeys(r).filter(d=>a.indexOf(d)!==-1),i={...r,...e};for(const d of o){const c=Tr(r[d],e[d]);if(!c.valid)return{valid:!1};i[d]=c.data}return{valid:!0,data:i}}if(t===B.array&&s===B.array){if(r.length!==e.length)return{valid:!1};const a=[];for(let o=0;o<r.length;o++){const i=Tr(r[o],e[o]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}return t===B.date&&s===B.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class Ps extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=(o,i)=>{if(Mr(o)||Mr(i))return Ce;const d=Tr(o.value,i.value);return d.valid?((Ar(o)||Ar(i))&&t.dirty(),{status:t.value,value:d.data}):(D(s,{code:S.invalid_intersection_types}),Ce)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([o,i])=>a(o,i)):a(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}Ps.create=(r,e,t)=>new Ps({left:r,right:e,typeName:_e.ZodIntersection,...Ie(t)});class Pt extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.array)return D(s,{code:S.invalid_type,expected:B.array,received:s.parsedType}),Ce;if(s.data.length<this._def.items.length)return D(s,{code:S.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Ce;!this._def.rest&&s.data.length>this._def.items.length&&(D(s,{code:S.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...s.data].map((o,i)=>{const d=this._def.items[i]||this._def.rest;return d?d._parse(new Ot(s,o,s.path,i)):null}).filter(o=>!!o);return s.common.async?Promise.all(a).then(o=>yt.mergeArray(t,o)):yt.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new Pt({...this._def,rest:e})}}Pt.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Pt({items:r,typeName:_e.ZodTuple,rest:null,...Ie(e)})};class ur extends Le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.object)return D(s,{code:S.invalid_type,expected:B.object,received:s.parsedType}),Ce;const a=[],o=this._def.keyType,i=this._def.valueType;for(const d in s.data)a.push({key:o._parse(new Ot(s,d,s.path,d)),value:i._parse(new Ot(s,s.data[d],s.path,d)),alwaysSet:d in s.data});return s.common.async?yt.mergeObjectAsync(t,a):yt.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,s){return new ur(t instanceof Le?{keyType:e,valueType:t,typeName:_e.ZodRecord,...Ie(s)}:{keyType:Tt.create(),valueType:e,typeName:_e.ZodRecord,...Ie(t)})}}class lr extends Le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.map)return D(s,{code:S.invalid_type,expected:B.map,received:s.parsedType}),Ce;const a=this._def.keyType,o=this._def.valueType,i=[...s.data.entries()].map(([d,c],u)=>({key:a._parse(new Ot(s,d,s.path,[u,"key"])),value:o._parse(new Ot(s,c,s.path,[u,"value"]))}));if(s.common.async){const d=new Map;return Promise.resolve().then(async()=>{for(const c of i){const u=await c.key,p=await c.value;if(u.status==="aborted"||p.status==="aborted")return Ce;u.status!=="dirty"&&p.status!=="dirty"||t.dirty(),d.set(u.value,p.value)}return{status:t.value,value:d}})}{const d=new Map;for(const c of i){const u=c.key,p=c.value;if(u.status==="aborted"||p.status==="aborted")return Ce;u.status!=="dirty"&&p.status!=="dirty"||t.dirty(),d.set(u.value,p.value)}return{status:t.value,value:d}}}}lr.create=(r,e,t)=>new lr({valueType:e,keyType:r,typeName:_e.ZodMap,...Ie(t)});class os extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.set)return D(s,{code:S.invalid_type,expected:B.set,received:s.parsedType}),Ce;const a=this._def;a.minSize!==null&&s.data.size<a.minSize.value&&(D(s,{code:S.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),a.maxSize!==null&&s.data.size>a.maxSize.value&&(D(s,{code:S.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const o=this._def.valueType;function i(c){const u=new Set;for(const p of c){if(p.status==="aborted")return Ce;p.status==="dirty"&&t.dirty(),u.add(p.value)}return{status:t.value,value:u}}const d=[...s.data.values()].map((c,u)=>o._parse(new Ot(s,c,s.path,u)));return s.common.async?Promise.all(d).then(c=>i(c)):i(d)}min(e,t){return new os({...this._def,minSize:{value:e,message:ie.toString(t)}})}max(e,t){return new os({...this._def,maxSize:{value:e,message:ie.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}os.create=(r,e)=>new os({valueType:r,minSize:null,maxSize:null,typeName:_e.ZodSet,...Ie(e)});class ms extends Le{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.function)return D(t,{code:S.invalid_type,expected:B.function,received:t.parsedType}),Ce;function s(d,c){return rr({data:d,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,sr(),ys].filter(u=>!!u),issueData:{code:S.invalid_arguments,argumentsError:c}})}function a(d,c){return rr({data:d,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,sr(),ys].filter(u=>!!u),issueData:{code:S.invalid_return_type,returnTypeError:c}})}const o={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Cs){const d=this;return _t(async function(...c){const u=new wt([]),p=await d._def.args.parseAsync(c,o).catch($=>{throw u.addIssue(s(c,$)),u}),oe=await Reflect.apply(i,this,p);return await d._def.returns._def.type.parseAsync(oe,o).catch($=>{throw u.addIssue(a(oe,$)),u})})}{const d=this;return _t(function(...c){const u=d._def.args.safeParse(c,o);if(!u.success)throw new wt([s(c,u.error)]);const p=Reflect.apply(i,this,u.data),oe=d._def.returns.safeParse(p,o);if(!oe.success)throw new wt([a(p,oe.error)]);return oe.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ms({...this._def,args:Pt.create(e).rest(ss.create())})}returns(e){return new ms({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new ms({args:e||Pt.create([]).rest(ss.create()),returns:t||ss.create(),typeName:_e.ZodFunction,...Ie(s)})}}class Zs extends Le{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Zs.create=(r,e)=>new Zs({getter:r,typeName:_e.ZodLazy,...Ie(e)});class Fs extends Le{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return D(t,{received:t.data,code:S.invalid_literal,expected:this._def.value}),Ce}return{status:"valid",value:e.data}}get value(){return this._def.value}}function wa(r,e){return new Kt({values:r,typeName:_e.ZodEnum,...Ie(e)})}Fs.create=(r,e)=>new Fs({value:r,typeName:_e.ZodLiteral,...Ie(e)});class Kt extends Le{constructor(){super(...arguments),Ms.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return D(t,{expected:Ve.joinValues(s),received:t.parsedType,code:S.invalid_type}),Ce}if(nr(this,Ms)||fa(this,Ms,new Set(this._def.values)),!nr(this,Ms).has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return D(t,{received:t.data,code:S.invalid_enum_value,options:s}),Ce}return _t(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Kt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Kt.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}Ms=new WeakMap,Kt.create=wa;class js extends Le{constructor(){super(...arguments),As.set(this,void 0)}_parse(e){const t=Ve.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==B.string&&s.parsedType!==B.number){const a=Ve.objectValues(t);return D(s,{expected:Ve.joinValues(a),received:s.parsedType,code:S.invalid_type}),Ce}if(nr(this,As)||fa(this,As,new Set(Ve.getValidEnumValues(this._def.values))),!nr(this,As).has(e.data)){const a=Ve.objectValues(t);return D(s,{received:s.data,code:S.invalid_enum_value,options:a}),Ce}return _t(e.data)}get enum(){return this._def.values}}As=new WeakMap,js.create=(r,e)=>new js({values:r,typeName:_e.ZodNativeEnum,...Ie(e)});class Cs extends Le{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.promise&&t.common.async===!1)return D(t,{code:S.invalid_type,expected:B.promise,received:t.parsedType}),Ce;const s=t.parsedType===B.promise?t.data:Promise.resolve(t.data);return _t(s.then(a=>this._def.type.parseAsync(a,{path:t.path,errorMap:t.common.contextualErrorMap})))}}Cs.create=(r,e)=>new Cs({type:r,typeName:_e.ZodPromise,...Ie(e)});class Nt extends Le{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===_e.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=this._def.effect||null,o={addIssue:i=>{D(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(o.addIssue=o.addIssue.bind(o),a.type==="preprocess"){const i=a.transform(s.data,o);if(s.common.async)return Promise.resolve(i).then(async d=>{if(t.value==="aborted")return Ce;const c=await this._def.schema._parseAsync({data:d,path:s.path,parent:s});return c.status==="aborted"?Ce:c.status==="dirty"||t.value==="dirty"?ar(c.value):c});{if(t.value==="aborted")return Ce;const d=this._def.schema._parseSync({data:i,path:s.path,parent:s});return d.status==="aborted"?Ce:d.status==="dirty"||t.value==="dirty"?ar(d.value):d}}if(a.type==="refinement"){const i=d=>{const c=a.refinement(d,o);if(s.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return d};if(s.common.async===!1){const d=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return d.status==="aborted"?Ce:(d.status==="dirty"&&t.dirty(),i(d.value),{status:t.value,value:d.value})}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(d=>d.status==="aborted"?Ce:(d.status==="dirty"&&t.dirty(),i(d.value).then(()=>({status:t.value,value:d.value}))))}if(a.type==="transform"){if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!ns(i))return i;const d=a.transform(i.value,o);if(d instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:d}}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>ns(i)?Promise.resolve(a.transform(i.value,o)).then(d=>({status:t.value,value:d})):i)}Ve.assertNever(a)}}Nt.create=(r,e,t)=>new Nt({schema:r,typeName:_e.ZodEffects,effect:e,...Ie(t)}),Nt.createWithPreprocess=(r,e,t)=>new Nt({schema:e,effect:{type:"preprocess",transform:r},typeName:_e.ZodEffects,...Ie(t)});class zt extends Le{_parse(e){return this._getType(e)===B.undefined?_t(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}zt.create=(r,e)=>new zt({innerType:r,typeName:_e.ZodOptional,...Ie(e)});class Yt extends Le{_parse(e){return this._getType(e)===B.null?_t(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Yt.create=(r,e)=>new Yt({innerType:r,typeName:_e.ZodNullable,...Ie(e)});class Ds extends Le{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===B.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Ds.create=(r,e)=>new Ds({innerType:r,typeName:_e.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...Ie(e)});class Us extends Le{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return Is(a)?a.then(o=>({status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new wt(s.common.issues)},input:s.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new wt(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}Us.create=(r,e)=>new Us({innerType:r,typeName:_e.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...Ie(e)});class dr extends Le{_parse(e){if(this._getType(e)!==B.nan){const t=this._getOrReturnCtx(e);return D(t,{code:S.invalid_type,expected:B.nan,received:t.parsedType}),Ce}return{status:"valid",value:e.data}}}dr.create=r=>new dr({typeName:_e.ZodNaN,...Ie(r)});const ei=Symbol("zod_brand");class Lr extends Le{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class Bs extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const a=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?Ce:a.status==="dirty"?(t.dirty(),ar(a.value)):this._def.out._parseAsync({data:a.value,path:s.path,parent:s})})();{const a=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?Ce:a.status==="dirty"?(t.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:s.path,parent:s})}}static create(e,t){return new Bs({in:e,out:t,typeName:_e.ZodPipeline})}}class Vs extends Le{_parse(e){const t=this._def.innerType._parse(e),s=a=>(ns(a)&&(a.value=Object.freeze(a.value)),a);return Is(t)?t.then(a=>s(a)):s(t)}unwrap(){return this._def.innerType}}function Gr(r,e={},t){return r?_s.create().superRefine((s,a)=>{var o,i;if(!r(s)){const d=typeof e=="function"?e(s):typeof e=="string"?{message:e}:e,c=(i=(o=d.fatal)!==null&&o!==void 0?o:t)===null||i===void 0||i,u=typeof d=="string"?{message:d}:d;a.addIssue({code:"custom",...u,fatal:c})}}):_s.create()}Vs.create=(r,e)=>new Vs({innerType:r,typeName:_e.ZodReadonly,...Ie(e)});const ti={object:Xe.lazycreate};var _e;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(_e||(_e={}));const Jr=Tt.create,Wr=Jt.create,si=dr.create,ri=Wt.create,Kr=Rs.create,ai=is.create,ni=ir.create,ii=zs.create,oi=Ls.create,li=_s.create,di=ss.create,ci=Ut.create,ui=or.create,hi=It.create,pi=Xe.create,vi=Xe.strictCreate,mi=Os.create,gi=cr.create,fi=Ps.create,yi=Pt.create,_i=ur.create,Ci=lr.create,wi=os.create,bi=ms.create,$i=Zs.create,Si=Fs.create,ki=Kt.create,xi=js.create,Mi=Cs.create,Yr=Nt.create,Ai=zt.create,Ti=Yt.create,Ni=Nt.createWithPreprocess,Ei=Bs.create,Ii={string:r=>Tt.create({...r,coerce:!0}),number:r=>Jt.create({...r,coerce:!0}),boolean:r=>Rs.create({...r,coerce:!0}),bigint:r=>Wt.create({...r,coerce:!0}),date:r=>is.create({...r,coerce:!0})},Ri=Ce;var He=Object.freeze({__proto__:null,defaultErrorMap:ys,setErrorMap:function(r){ga=r},getErrorMap:sr,makeIssue:rr,EMPTY_PATH:[],addIssueToContext:D,ParseStatus:yt,INVALID:Ce,DIRTY:ar,OK:_t,isAborted:Mr,isDirty:Ar,isValid:ns,isAsync:Is,get util(){return Ve},get objectUtil(){return xr},ZodParsedType:B,getParsedType:Ft,ZodType:Le,datetimeRegex:Ca,ZodString:Tt,ZodNumber:Jt,ZodBigInt:Wt,ZodBoolean:Rs,ZodDate:is,ZodSymbol:ir,ZodUndefined:zs,ZodNull:Ls,ZodAny:_s,ZodUnknown:ss,ZodNever:Ut,ZodVoid:or,ZodArray:It,ZodObject:Xe,ZodUnion:Os,ZodDiscriminatedUnion:cr,ZodIntersection:Ps,ZodTuple:Pt,ZodRecord:ur,ZodMap:lr,ZodSet:os,ZodFunction:ms,ZodLazy:Zs,ZodLiteral:Fs,ZodEnum:Kt,ZodNativeEnum:js,ZodPromise:Cs,ZodEffects:Nt,ZodTransformer:Nt,ZodOptional:zt,ZodNullable:Yt,ZodDefault:Ds,ZodCatch:Us,ZodNaN:dr,BRAND:ei,ZodBranded:Lr,ZodPipeline:Bs,ZodReadonly:Vs,custom:Gr,Schema:Le,ZodSchema:Le,late:ti,get ZodFirstPartyTypeKind(){return _e},coerce:Ii,any:li,array:hi,bigint:ri,boolean:Kr,date:ai,discriminatedUnion:gi,effect:Yr,enum:ki,function:bi,instanceof:(r,e={message:`Input not instance of ${r.name}`})=>Gr(t=>t instanceof r,e),intersection:fi,lazy:$i,literal:Si,map:Ci,nan:si,nativeEnum:xi,never:ci,null:oi,nullable:Ti,number:Wr,object:pi,oboolean:()=>Kr().optional(),onumber:()=>Wr().optional(),optional:Ai,ostring:()=>Jr().optional(),pipeline:Ei,preprocess:Ni,promise:Mi,record:_i,set:wi,strictObject:vi,string:Jr,symbol:ni,transformer:Yr,tuple:yi,undefined:ii,union:mi,unknown:di,void:ui,NEVER:Ri,ZodIssueCode:S,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:wt});class ft extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,ft.prototype)}}const Dt=He.object({name:He.string().optional(),title:He.string().optional(),type:He.enum(["stdio","http","sse"]).optional(),command:He.string().optional(),args:He.array(He.union([He.string(),He.number(),He.boolean()])).optional(),env:He.record(He.union([He.string(),He.number(),He.boolean(),He.null(),He.undefined()])).optional(),url:He.string().optional()}).passthrough();function xt(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function us(r){return(r==null?void 0:r.type)==="stdio"}function Ys(r){return xt(r)?r.url:us(r)?r.command:""}const zi=He.array(Dt),Li=He.object({servers:He.array(Dt)}),Oi=He.object({mcpServers:He.array(Dt)}),Pi=He.object({servers:He.record(He.unknown())}),Zi=He.object({mcpServers:He.record(He.unknown())}),Fi=He.record(He.unknown()),ji=Dt.refine(r=>{const e=r.command!==void 0,t=r.url!==void 0;if(!e&&!t)return!1;const s=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(a=>s.has(a))},{message:"Single server object must have valid server properties"});function Xt(r){try{const e=Dt.transform(t=>{let s;if(t.type)s=t.type;else if(t.url)s="http";else{if(!t.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");s="stdio"}if(s==="http"||s==="sse"){if(!t.url)throw new Error(`${s.toUpperCase()} server must have a 'url' property`);return{type:s,name:t.name||t.title||t.url,url:t.url}}{const a=t.command||"",o=t.args?t.args.map(u=>String(u)):[];if(!a)throw new Error("Stdio server must have a 'command' property");const i=o.length>0?`${a} ${o.join(" ")}`:a,d=t.name||t.title||(a?a.split(" ")[0]:""),c=t.env?Object.fromEntries(Object.entries(t.env).filter(([u,p])=>p!=null).map(([u,p])=>[u,String(p)])):void 0;return{type:"stdio",name:d,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>t.type==="http"||t.type==="sse"?!!t.url:!!t.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!e.success)throw new ft(e.error.message);return e.data}catch(e){throw e instanceof Error?new ft(`Invalid server configuration: ${e.message}`):new ft("Invalid server configuration")}}class Gs{constructor(e){Ze(this,"servers",ht([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===Je.getStoredMCPServersResponse){const s=t.data;return Array.isArray(s)&&this.servers.set(s),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:Je.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:Je.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new ft("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const s=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(s),s})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const s=[...t,...e.map(a=>({...a,id:crypto.randomUUID()}))];return this.saveServers(s),s})}checkExistingServerName(e,t){const s=Cr(this.servers).find(a=>a.name===e);if(s&&(s==null?void 0:s.id)!==t)throw new ft(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const s=t.map(a=>a.id===e.id?e:a);return this.saveServers(s),s})}deleteServer(e){this.servers.update(t=>{const s=t.filter(a=>a.id!==e.id);return this.saveServers(s),s}),e.type==="http"&&e.authRequired&&this.host.postMessage({type:Je.deleteOAuthSession,data:e.name})}toggleDisabledServer(e){this.servers.update(t=>{const s=t.map(a=>a.id===e?{...a,disabled:!a.disabled}:a);return this.saveServers(s),s})}static convertServerToJSON(e){if(xt(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,s=new Map;e.forEach(o=>{var d,c;const i=(d=o.tools)==null?void 0:d.filter(u=>!u.enabled).map(u=>u.definition.mcp_tool_name);o.disabled?t.set(o.id,"MCP server has been manually disabled"):o.tools&&o.tools.length===0?t.set(o.id,"No tools are available for this MCP server"):i&&i.length===((c=o.tools)==null?void 0:c.length)?t.set(o.id,"All tools for this MCP server have validation errors: "+i.join(", ")):i&&i.length>0&&s.set(o.id,"MCP server has validation errors in the following tools which have been disabled: "+i.join(", "))});const a=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...a]),warnings:s}}static parseDuplicateServerIds(e){const t=new Map;for(const a of e)t.has(a.name)||t.set(a.name,[]),t.get(a.name).push(a.id);const s=new Map;for(const[,a]of t)if(a.length>1)for(let o=1;o<a.length;o++)s.set(a[o],"MCP server is disabled due to duplicate server names");return s}static convertParsedServerToWebview(e){const{tools:t,...s}=e;return{...s,tools:void 0}}static parseServerConfigFromJSON(e){return function(s){try{const a=JSON.parse(s),o=He.union([zi.transform(i=>i.map(d=>Xt(d))),Li.transform(i=>i.servers.map(d=>Xt(d))),Oi.transform(i=>i.mcpServers.map(d=>Xt(d))),Pi.transform(i=>Object.entries(i.servers).map(([d,c])=>{const u=Dt.parse(c);return Xt({...u,name:u.name||d})})),Zi.transform(i=>Object.entries(i.mcpServers).map(([d,c])=>{const u=Dt.parse(c);return Xt({...u,name:u.name||d})})),ji.transform(i=>[Xt(i)]),Fi.transform(i=>{if(!Object.values(i).some(d=>{const c=Dt.safeParse(d);return c.success&&(c.data.command!==void 0||c.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(i).map(([d,c])=>{const u=Dt.parse(c);return Xt({...u,name:u.name||d})})})]).safeParse(a);if(o.success)return o.data;throw new ft("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(a){throw a instanceof ft?a:new ft("Failed to parse MCP servers from JSON. Please check the format.")}}(e).map(s=>this.convertParsedServerToWebview(s))}importFromJSON(e){try{const t=Gs.parseServerConfigFromJSON(e),s=Cr(this.servers),a=new Set(s.map(o=>o.name));for(const o of t){if(!o.name)throw new ft("All servers must have a name.");if(a.has(o.name))throw new ft(`A server with the name '${o.name}' already exists.`);a.add(o.name)}return this.servers.update(o=>{const i=[...o,...t.map(d=>({...d,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof ft?t:new ft("Failed to import MCP servers from JSON. Please check the format.")}}}class Di{constructor(e){Ze(this,"_terminalSettings",ht({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===Je.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:Je.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:Je.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:Je.updateTerminalSettings,data:{startupScript:e}})}}const Ts=class Ts{constructor(e){Ze(this,"_swarmModeSettings",ht(Ks));Ze(this,"_isLoaded",!1);Ze(this,"_pollInterval",null);Ze(this,"_lastKnownSettingsHash","");Ze(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:fr.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(Ks),this._lastKnownSettingsHash=JSON.stringify(Ks),this._isLoaded=!0}}async updateSettings(e){try{const t=await this._msgBroker.sendToSidecar({type:fr.updateSwarmModeSettings,data:e});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data))}catch(t){throw console.error("Failed to update swarm mode settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(Ks)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Ts.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:fr.getSwarmModeSettings}),t=JSON.stringify(e.data);this._lastKnownSettingsHash&&t!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=t}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}};Ze(Ts,"key","swarmModeModel"),Ze(Ts,"POLLING_INTERVAL_MS",5e3);let qs=Ts;var Rt=(r=>(r.file="file",r.folder="folder",r))(Rt||{});class Gt{constructor(e,t){Ze(this,"subscribe");Ze(this,"set");Ze(this,"update");Ze(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case Je.wsContextSourceFoldersChanged:case Je.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case Je.sourceFoldersSyncStatus:this.update(s=>({...s,syncStatus:t.data.status}))}});Ze(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:Je.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);Ze(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:Je.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,s)=>t.type===s.type?t.name.localeCompare(s.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:s,set:a,update:o}=ht({sourceFolders:[],sourceTree:[],syncStatus:wr.done});this.subscribe=s,this.set=a,this.update=o,this.getSourceFolders().then(i=>{this.update(d=>({...d,sourceFolders:i,sourceTree:Gt.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==kt.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:Je.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:Je.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:Je.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=Cr(this);const s=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(a=>({...a,sourceFolders:e,sourceTree:s}))}async getRefreshedSourceTree(e,t){const s=Gt.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,s)}async getRefreshedSourceTreeRecurse(e,t){const s=new Map(e.map(a=>[JSON.stringify([a.fileId.folderRoot,a.fileId.relPath]),a]));for(let a of t){const o=Gt.fileIdToString(a.fileId);if(a.type==="folder"){const i=s.get(o);i&&(a.expanded=i.type==="folder"&&i.expanded,a.expanded&&(a.children=await this.getChildren(a.fileId),a.children=await this.getRefreshedSourceTreeRecurse(i.children,a.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,s)=>t.name.localeCompare(s.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}var Ui=g('<div><!> <!> <span class="name svelte-1skknri"> <span class="folderRoot svelte-1skknri"> </span></span> <!></div>'),Vi=g('<div class="source-folder svelte-1skknri"><!> <div role="button" tabindex="0" class="add-more svelte-1skknri"><!> Add more...</div></div>');const qi="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",Hi="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",Bi="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e";var Gi=g('<div class="children-container"></div>'),Ji=g('<div><div role="treeitem" aria-selected="false" tabindex="0"><!> <span class="name svelte-sympus"> </span> <!> <img/></div> <!></div>');function ba(r,e){st(e,!1);let t=w(e,"data",8),s=w(e,"wsContextModel",8),a=w(e,"indentLevel",8);const o=()=>{s().toggleNode(t())},i={[kt.included]:qi,[kt.excluded]:Hi,[kt.partial]:Bi},d={[kt.included]:"included",[kt.excluded]:"excluded",[kt.partial]:"partially included"};let c=j(),u=j(),p=j();ve(()=>C(t()),()=>{var P;m(u,(P=t()).type===Rt.folder&&P.inclusionState!==kt.excluded?P.expanded?"chevron-down":"chevron-right":P.type===Rt.folder?"folder":"file")}),ve(()=>(C(t()),kt),()=>{m(c,t().type===Rt.folder&&t().inclusionState!==kt.excluded)}),ve(()=>(C(t()),Rt),()=>{m(p,t().type===Rt.folder&&t().expanded&&t().children&&t().children.length>0?t():null)}),dt(),nt();var oe=Ji(),$=h(oe),de=ps(()=>Qs("Enter",o));let me;var Z=h($);ua(Z,{get icon(){return n(u)}});var k=v(Z,2),I=h(k),q=v(k,2),G=P=>{ce(P,{size:1,class:"file-count",children:(A,E)=>{var K=N();fe(O=>ze(K,O),[()=>(C(t()),f(()=>t().trackedFileCount.toLocaleString()))],xe),l(A,K)},$$slots:{default:!0}})};U(q,P=>{C(t()),C(Rt),C(kt),f(()=>t().type===Rt.folder&&t().inclusionState!==kt.excluded&&typeof t().trackedFileCount=="number")&&P(G)});var T=v(q,2),y=v($,2),L=P=>{var A=Gi();pt(A,5,()=>(n(p),f(()=>n(p).children)),E=>Gt.fileIdToString(E.fileId),(E,K)=>{var O=lt(),_=$e(O);const b=xe(()=>a()+1);ba(_,{get data(){return n(K)},get wsContextModel(){return s()},get indentLevel(){return n(b)}}),l(E,O)}),l(P,A)};U(y,P=>{n(p)&&P(L)}),fe(P=>{me=At($,1,"tree-item svelte-sympus",null,me,P),es($,"title",(C(t()),f(()=>t().reason))),es($,"aria-expanded",(C(t()),C(Rt),f(()=>t().type===Rt.folder&&t().expanded))),es($,"aria-level",a()),Ia($,`padding-left: ${10*a()+20}px;`),ze(I,(C(t()),f(()=>t().name))),es(T,"src",(C(t()),f(()=>i[t().inclusionState]))),es(T,"alt",(C(t()),f(()=>d[t().inclusionState])))},[()=>({"included-folder":n(c)})],xe),Ct("click",$,o),Ct("keyup",$,function(...P){var A;(A=n(de))==null||A.apply(this,P)}),l(r,oe),rt()}var Wi=g('<div class="files-container svelte-8hfqhl"></div>'),Ki=bt('<svg width="15" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" transform="matrix(-1 0 0 -1 16 16)" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z" fill="currentColor"></path></svg>');function Yi(r){var e=Ki();l(r,e)}var Qi=g('<div class="icon-wrapper svelte-13uht7n"><!></div>'),Xi=g("<!> <!>",1),eo=g('<div class="settings-card-body"><!></div>'),to=g('<div><div class="settings-card-content svelte-13uht7n"><div class="settings-card-left svelte-13uht7n"><!></div> <div class="settings-card-right svelte-13uht7n"><!></div></div> <!></div>');function Lt(r,e){const t=Ra(e),s=rs(e,["children","$$slots","$$events","$$legacy"]),a=rs(s,["class","icon","title","isClickable"]);st(e,!1);const o=j(),i=j(),d=j();let c=w(e,"class",8,""),u=w(e,"icon",24,()=>{}),p=w(e,"title",24,()=>{}),oe=w(e,"isClickable",8,!1);ve(()=>(n(o),n(i),C(a)),()=>{m(o,a.class),m(i,za(a,["class"]))}),ve(()=>(C(c()),n(o)),()=>{m(d,`settings-card ${c()} ${n(o)||""}`)}),dt();var $=to();ws($,L=>({role:"button",class:n(d),...n(i),[La]:L}),[()=>({clickable:oe()})],"svelte-13uht7n");var de=h($),me=h(de),Z=h(me),k=L=>{var P=Xi(),A=$e(P),E=_=>{var b=Qi(),ue=h(b);kr(ue,u,(V,x)=>{x(V,{})}),l(_,b)};U(A,_=>{u()&&_(E)});var K=v(A,2),O=_=>{ce(_,{color:"neutral",size:1,weight:"light",class:"card-title",children:(b,ue)=>{var V=N();fe(()=>ze(V,p())),l(b,V)},$$slots:{default:!0}})};U(K,_=>{p()&&_(O)}),l(L,P)},I=L=>{var P=lt(),A=$e(P);Mt(A,e,"header-left",{},null),l(L,P)};U(Z,L=>{u()||p()?L(k):L(I,!1)});var q=v(me,2),G=h(q);Mt(G,e,"header-right",{},null);var T=v(de,2),y=L=>{var P=eo(),A=h(P);Mt(A,e,"default",{},null),l(L,P)};U(T,L=>{f(()=>t.default)&&L(y)}),Ct("click",$,function(L){ja.call(this,e,L)}),l(r,$),rt()}var so=g('<div class="context-list svelte-qsnirf"><div><!> <!></div> <div><div class="files-header svelte-qsnirf"><!> <!></div> <!></div></div>'),ro=g('<div slot="header-right"><!></div>');function ao(r,e){st(e,!1);const[t,s]=Et(),a=()=>et(i,"$wsContextModel",t),o=j();let i=new Gt(ut,new mn(ut.postMessage)),d=j(),c=j();ve(()=>a(),()=>{m(d,a().sourceFolders.sort((u,p)=>u.isWorkspaceFolder!==p.isWorkspaceFolder?u.isWorkspaceFolder?-1:1:u.fileId.folderRoot.localeCompare(p.fileId.folderRoot)))}),ve(()=>a(),()=>{m(c,a().syncStatus)}),ve(()=>n(d),()=>{m(o,n(d).reduce((u,p)=>u+(p.trackedFileCount??0),0))}),dt(),nt(),Ct("message",Ir,function(...u){var p;(p=i.handleMessageFromExtension)==null||p.apply(this,u)}),Lt(r,{get icon(){return Yi},title:"Context",$$events:{contextmenu:u=>u.preventDefault()},children:(u,p)=>{var oe=so(),$=h(oe),de=h($);ce(de,{size:1,weight:"medium",class:"context-section-header",children:(q,G)=>{var T=N("SOURCE FOLDERS");l(q,T)},$$slots:{default:!0}}),function(q,G){st(G,!1);let T=w(G,"folders",24,()=>[]),y=w(G,"onAddMore",8),L=w(G,"onRemove",8);nt();var P=Vi(),A=h(P);pt(A,1,T,_=>Gt.fileIdToString(_.fileId),(_,b)=>{var ue=Ui();let V;var x=h(ue),te=ye=>{var je=ps(()=>Qs("Enter",()=>L()(n(b).fileId.folderRoot)));as(ye,{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$events:{click:()=>L()(n(b).fileId.folderRoot),keyup(...se){var Y;(Y=n(je))==null||Y.apply(this,se)}},children:(se,Y)=>{hn(se)},$$slots:{default:!0}})};U(x,ye=>{n(b),f(()=>!n(b).isWorkspaceFolder)&&ye(te)});var H=v(x,2);const ae=xe(()=>(n(b),f(()=>(ye=>ye.isWorkspaceFolder?"root-folder":"folder")(n(b)))));ua(H,{class:"source-folder-v-adjust",get icon(){return n(ae)}});var he=v(H,2),Te=h(he),Me=v(Te),F=h(Me),pe=v(he,2),Ne=ye=>{ce(ye,{size:1,class:"file-count",children:(je,se)=>{var Y=N();fe(Oe=>ze(Y,Oe),[()=>(n(b),f(()=>n(b).trackedFileCount.toLocaleString()))],xe),l(je,Y)},$$slots:{default:!0}})};U(pe,ye=>{n(b),f(()=>n(b).trackedFileCount)&&ye(Ne)}),fe(ye=>{V=At(ue,1,"item svelte-1skknri",null,V,ye),ze(Te,`${n(b),f(()=>n(b).name)??""} `),ze(F,(n(b),f(()=>n(b).isPending?"(pending)":n(b).fileId.folderRoot)))},[()=>({"workspace-folder":n(b).isWorkspaceFolder})],xe),l(_,ue)});var E=v(A,2),K=ps(()=>Qs("Enter",y())),O=h(E);gs(O,{}),Ct("keyup",E,function(..._){var b;(b=n(K))==null||b.apply(this,_)}),Ct("click",E,function(..._){var b;(b=y())==null||b.apply(this,_)}),l(q,P),rt()}(v(de,2),{get folders(){return n(d)},onRemove:q=>i.removeSourceFolder(q),onAddMore:()=>i.addMoreSourceFolders()});var me=v($,2),Z=h(me),k=h(Z);ce(k,{size:1,weight:"medium",class:"context-section-header",children:(q,G)=>{var T=N("FILES");l(q,T)},$$slots:{default:!0}});var I=v(k,2);ce(I,{size:1,class:"file-count",children:(q,G)=>{var T=N();fe(y=>ze(T,y),[()=>(n(o),f(()=>n(o).toLocaleString()))],xe),l(q,T)},$$slots:{default:!0}}),function(q,G){st(G,!1);const[T,y]=Et(),L=()=>et(P(),"$wsContextModel",T);let P=w(G,"wsContextModel",8),A=j();ve(()=>L(),()=>{m(A,L().sourceTree)}),dt(),nt();var E=Wi();pt(E,5,()=>n(A),K=>Gt.fileIdToString(K.fileId),(K,O)=>{ba(K,{get wsContextModel(){return P()},get data(){return n(O)},indentLevel:0})}),l(q,E),rt(),y()}(v(Z,2),{get wsContextModel(){return i}}),l(u,oe)},$$slots:{default:!0,"header-right":(u,p)=>{var oe=ro(),$=h(oe),de=me=>{var Z=ps(()=>Qs("Enter",()=>i.requestRefresh()));as(me,{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$events:{click:()=>i.requestRefresh(),keyup(...k){var I;(I=n(Z))==null||I.apply(this,k)}},children:(k,I)=>{Ya(k)},$$slots:{default:!0}})};U($,me=>{n(c),C(wr),f(()=>n(c)===wr.done)&&me(de)}),l(u,oe)}}}),rt(),s()}function Nr(r){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function Qr(r){return Nr(r)&&"component"in r}var no=bt('<svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z" fill="currentColor"></path></svg>');function Xr(r){var e=no();l(r,e)}var io=g('<div class="c-navigation__content-header svelte-z0ijuz"> </div>'),oo=g('<div class="c-navigation__content-description svelte-z0ijuz"> </div>'),lo=g('<!> <!> <div class="c-navigation__content-container svelte-z0ijuz"><!></div>',1),co=g('<div class="c-navigation__content svelte-z0ijuz"><!> <div><!></div></div>');function ea(r,e){st(e,!1);let t=w(e,"item",8);nt();var s=co(),a=h(s);Mt(a,e,"header",{},null);var o=v(a,2),i=h(o),d=c=>{var u=lo(),p=$e(u);ce(p,{size:4,weight:"medium",color:"neutral",children:(Z,k)=>{var I=io(),q=h(I);fe(()=>ze(q,(C(t()),f(()=>{var G;return(G=t())==null?void 0:G.name})))),l(Z,I)},$$slots:{default:!0}});var oe=v(p,2),$=Z=>{ce(Z,{color:"secondary",size:1,weight:"light",children:(k,I)=>{var q=oo(),G=h(q);fe(()=>ze(G,(C(t()),f(()=>{var T;return(T=t())==null?void 0:T.description})))),l(k,q)},$$slots:{default:!0}})};U(oe,Z=>{C(t()),f(()=>{var k;return(k=t())==null?void 0:k.description})&&Z($)});var de=v(oe,2),me=h(de);Mt(me,e,"content",{get item(){return t()}},null),l(c,u)};U(i,c=>{t()!=null&&c(d)}),fe(()=>es(s,"id",(C(t()),f(()=>{var c;return(c=t())==null?void 0:c.id})))),l(r,s),rt()}function xs(r,e,t,s,a,o){return a!==void 0?{name:r,description:e,icon:t,id:s,component:a,props:o}:{name:r,description:e,icon:t,id:s}}var uo=g('<div class="c-navigation__head svelte-n5ccbo"><!> <!></div>'),ho=g('<span class="c-navigation__head-icon"><!></span> ',1),po=g("<button><!></button>"),vo=g('<div class="c-navigation__group"><!> <div class="c-navigation__items svelte-n5ccbo"></div></div>'),mo=g('<nav class="c-navigation__nav svelte-n5ccbo" slot="left"><!></nav>'),go=g('<span class="c-navigation__head-icon"><!></span> <span> </span>',1),fo=g("<span><!></span>"),yo=g('<div class="c-navigation__head svelte-n5ccbo"><!></div> <!>',1),_o=g('<div class="c-navigation__flat svelte-n5ccbo"><!> <!></div>'),Co=g("<div><!></div>");function wo(r,e){st(e,!1);let t=w(e,"group",8,"Workspace Settings"),s=w(e,"items",24,()=>[]),a=w(e,"item",28,()=>{}),o=w(e,"mode",8,"tree"),i=w(e,"selectedId",28,()=>{}),d=w(e,"onNavigationChangeItem",8,Z=>{}),c=w(e,"showButton",8,!0),u=w(e,"class",8,""),p=j(new Map);ve(()=>(C(i()),C(a()),C(s())),()=>{var Z;i()?a(s().find(k=>(k==null?void 0:k.id)===i())):i((Z=a())==null?void 0:Z.id)}),ve(()=>(C(s()),C(t())),()=>{m(p,s().reduce((Z,k)=>{if(!k)return Z;const I=k.group??t(),q=Z.get(I)??[];return q.push(k),Z.set(I,q),Z},new Map))}),ve(()=>(C(a()),C(s())),()=>{a()||a(s()[0])}),ve(()=>(C(d()),C(i())),()=>{d()(i())}),dt(),nt();var oe=Co(),$=h(oe),de=Z=>{yn(Z,{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,get showButton(){return c()},minimized:!1,$$slots:{left:(k,I)=>{var q=mo(),G=h(q);gn(G,i,T=>{var y=lt(),L=$e(y);pt(L,1,()=>n(p),Bt,(P,A)=>{var E=ps(()=>Zr(n(A),2));let K=()=>n(E)[0];var O=vo(),_=h(O);Mt(_,e,"group",{get label(){return K()},get mode(){return o()}},ue=>{var V=uo(),x=h(V);Xr(x);var te=v(x,2);ce(te,{size:2,color:"primary",children:(H,ae)=>{var he=N();fe(()=>ze(he,K())),l(H,he)},$$slots:{default:!0}}),l(ue,V)});var b=v(_,2);pt(b,5,()=>n(E)[1],Bt,(ue,V)=>{var x=po();let te;var H=h(x);ce(H,{size:2,weight:"regular",color:"primary",children:(ae,he)=>{var Te=ho(),Me=$e(Te),F=h(Me);kr(F,()=>n(V).icon,(Ne,ye)=>{ye(Ne,{})});var pe=v(Me);fe(()=>ze(pe,` ${n(V),f(()=>n(V).name)??""}`)),l(ae,Te)},$$slots:{default:!0}}),fe(ae=>te=At(x,1,"c-navigation__item svelte-n5ccbo",null,te,ae),[()=>({"is-active":n(V).id===i()})],xe),Ct("click",x,()=>{return ae=n(V),a(ae),void i(ae==null?void 0:ae.id);var ae}),l(ue,x)}),l(P,O)}),l(T,y)}),l(k,q)},right:(k,I)=>{ea(k,{get item(){return a()},slot:"right",$$slots:{header:(q,G)=>{var T=lt(),y=$e(T);Mt(y,e,"header",{get item(){return a()},get selectedId(){return i()}},null),l(q,T)},content:(q,G)=>{var T=lt(),y=$e(T);Mt(y,e,"content",{get item(){return a()},get isSelected(){return C(a()),C(i()),f(()=>{var L;return((L=a())==null?void 0:L.id)===i()})}},L=>{var P=lt(),A=$e(P),E=K=>{var O=lt(),_=$e(O);kr(_,()=>a().component,(b,ue)=>{ue(b,Oa(()=>a().props))}),l(K,O)};U(A,K=>{C(Qr),C(a()),C(o()),C(i()),f(()=>{return Qr(a())&&(O=a(),_=o(),b=i(),_!=="tree"||(O==null?void 0:O.id)===b);var O,_,b})&&K(E)}),l(L,P)}),l(q,T)}}})}}})},me=Z=>{var k=_o(),I=h(k);Mt(I,e,"header",{get item(){return a()}},null);var q=v(I,2);pt(q,1,()=>n(p),Bt,(G,T)=>{var y=ps(()=>Zr(n(T),2));let L=()=>n(y)[0];var P=yo(),A=$e(P),E=h(A);Mt(E,e,"group",{get label(){return L()},get mode(){return o()}},O=>{ce(O,{color:"secondary",size:2,weight:"medium",children:(_,b)=>{var ue=go(),V=$e(ue);Xr(h(V));var x=v(V,2),te=h(x);fe(()=>ze(te,L())),l(_,ue)},$$slots:{default:!0}})});var K=v(A,2);pt(K,1,()=>n(y)[1],Bt,(O,_)=>{var b=fo();ea(h(b),{get item(){return n(_)},$$slots:{content:(ue,V)=>{var x=lt(),te=$e(x);Mt(te,e,"content",{get item(){return n(_)}},null),l(ue,x)}}}),Da(b,(ue,V)=>function(x,te){let H;function ae({scrollTo:he,delay:Te,options:Me}){clearTimeout(H),he&&(H=setTimeout(()=>{x.scrollIntoView(Me)},Te))}return ae(te),{update:ae,destroy(){clearTimeout(H)}}}(ue,V),()=>({scrollTo:o()==="flat"&&n(_).id===i(),delay:300,options:{behavior:"smooth"}})),l(O,b)}),l(G,P)}),l(Z,k)};U($,Z=>{o()==="tree"?Z(de):Z(me,!1)}),fe(()=>At(oe,1,`c-navigation c-navigation--mode__${o()??""} ${u()??""}`,"svelte-n5ccbo")),l(r,oe),rt()}var bo=bt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z" fill="currentColor"></path></svg>');function $o(r){var e=bo();l(r,e)}var So=bt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z" fill="currentColor"></path></svg>');function ko(r){var e=So();l(r,e)}var xo=bt("<svg><!></svg>");function ta(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=xo();ws(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 17 16",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',!0),l(r,s)}var Mo=g('<div class="connect-button-spinner svelte-js5lik"><!></div> <span>Cancel</span>',1),Ao=g("<span>Connect</span>"),To=g('<div class="connect-button-content svelte-js5lik"><!></div>'),No=g('<div class="status-controls svelte-js5lik"><div class="icon-container svelte-js5lik"><div class="connection-status svelte-js5lik"><div><!></div></div> <!></div></div>'),Eo=g('<div slot="header-right"><!></div>'),Io=g("<div> </div>"),Ro=g('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),zo=g('<div class="loading-container svelte-2bsejd"><!> <!></div>'),Lo=g('<div class="category-content"><!></div>'),Oo=g('<div class="category"><div class="category-heading"><!></div> <!></div>');function $a(r,e){let t=w(e,"title",8),s=w(e,"loading",8,!1);var a=Oo(),o=h(a),i=h(o);ce(i,{size:1,color:"secondary",weight:"regular",children:(p,oe)=>{var $=N();fe(()=>ze($,t())),l(p,$)},$$slots:{default:!0}});var d=v(o,2),c=p=>{var oe=zo(),$=h(oe);tr($,{size:1});var de=v($,2);ce(de,{size:1,color:"secondary",children:(me,Z)=>{var k=N("Loading...");l(me,k)},$$slots:{default:!0}}),l(p,oe)},u=p=>{var oe=Lo(),$=h(oe);Mt($,e,"default",{},null),l(p,oe)};U(d,p=>{s()?p(c):p(u,!1)}),l(r,a)}const Sa="mcpServerModel";function Er(){const r=vs(Sa);if(!r)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return r}var Po=g('<div class="connect-button-spinner svelte-e3a21z"><!></div> <span>Cancel</span>',1),Zo=g("<span>Connect</span>"),Fo=g('<div class="connect-button-content svelte-e3a21z"><!></div>'),jo=g('<div class="status-controls svelte-e3a21z"><div><!></div> <!></div>'),Do=g('<div slot="header-right"><!></div>'),Uo=g("<div> </div>"),Vo=g('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),qo=g('<div class="tool-category-list svelte-on3wl5"><!> <!></div>'),Ho=g("<div><!></div>");function Bo(r,e){st(e,!1);const[t,s]=Et(),a=()=>et(Z,"$allServers",t),o=()=>et(me,"$pretendNativeToolDefs",t),i=j();let d=w(e,"title",8),c=w(e,"tools",24,()=>[]),u=w(e,"onAuthenticate",8),p=w(e,"onRevokeAccess",8),oe=w(e,"onToolApprovalConfigChange",8,()=>{});const $=vs(fs.key),de=Er(),me=$.getPretendNativeToolDefs(),Z=de.getServers();ve(()=>a(),()=>{m(i,$.getEnableNativeRemoteMcp()?na(a()):[])}),dt(),nt();var k=Ho(),I=h(k);const q=xe(()=>(C(c()),f(()=>c().length===0)));$a(I,{get title(){return d()},get loading(){return n(q)},children:(G,T)=>{var y=qo(),L=h(y);pt(L,1,c,A=>A.name,(A,E)=>{(function(K,O){st(O,!1);let _=w(O,"config",8),b=w(O,"onAuthenticate",8),ue=w(O,"onRevokeAccess",8);const V=()=>{};let x=j(!1),te=j(null),H=j(!1);function ae(){if(n(x))m(x,!1),n(te)&&(clearTimeout(n(te)),m(te,null));else{m(x,!0);const pe=_().authUrl||"";b()(pe),m(te,setTimeout(()=>{m(x,!1),m(te,null)},6e4))}}ve(()=>(C(_()),n(x),n(te)),()=>{_().isConfigured&&n(x)&&(m(x,!1),n(te)&&(clearTimeout(n(te)),m(te,null)))}),dt(),nt();var he=Ro(),Te=h(he);Lt(Te,{get icon(){return C(_()),f(()=>_().icon)},get title(){return C(_()),f(()=>_().displayName)},$$slots:{"header-right":(pe,Ne)=>{var ye=Eo(),je=h(ye),se=Oe=>{const Ae=xe(()=>n(x)?"neutral":"accent");Qe(Oe,{variant:"ghost-block",get color(){return n(Ae)},size:1,$$events:{click:ae},children:(Re,we)=>{var Se=To(),qe=h(Se),M=R=>{var re=Mo(),Q=$e(re),X=h(Q);tr(X,{size:1,useCurrentColor:!0}),l(R,re)},z=R=>{var re=Ao();l(R,re)};U(qe,R=>{n(x)?R(M):R(z,!1)}),l(Re,Se)},$$slots:{default:!0}})},Y=(Oe,Ae)=>{var Re=we=>{var Se=No(),qe=h(Se),M=h(qe),z=h(M);let R;var re=h(z);const Q=xe(()=>(C(hs),f(()=>[hs.Hover])));jt(re,{get triggerOn(){return n(Q)},content:"Revoke Access",children:(W,ne)=>{as(W,{color:"neutral",variant:"ghost",size:1,$$events:{click:()=>ue()(_())},children:(J,le)=>{ta(J,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var X=v(M,2);br.Root(X,{color:"success",size:1,variant:"soft",children:(W,ne)=>{var J=N("Connected");l(W,J)},$$slots:{default:!0}}),fe(W=>R=At(z,1,"icon-button-wrapper svelte-js5lik",null,R,W),[()=>({active:n(H)})],xe),l(we,Se)};U(Oe,we=>{C(_()),f(()=>_().isConfigured)&&we(Re)},Ae)};U(je,Oe=>{C(_()),f(()=>!_().isConfigured&&_().authUrl)?Oe(se):Oe(Y,!1)}),l(pe,ye)}}});var Me=v(Te,2),F=pe=>{var Ne=Io(),ye=h(Ne);fe(()=>{At(Ne,1,`status-message ${C(_()),f(()=>_().statusType)??""}`,"svelte-js5lik"),ze(ye,(C(_()),f(()=>_().statusMessage)))}),l(pe,Ne)};U(Me,pe=>{C(_()),f(()=>_().showStatus)&&pe(F)}),Ct("mouseenter",he,()=>m(H,!0)),Ct("mouseleave",he,()=>m(H,!1)),l(K,he),pa(O,"onToolApprovalConfigChange",V),rt({onToolApprovalConfigChange:V})})(A,{get config(){return n(E)},get onAuthenticate(){return u()},get onRevokeAccess(){return p()},onToolApprovalConfigChange:oe()})});var P=v(L,2);pt(P,1,o,A=>A.name,(A,E)=>{const K=xe(()=>(n(i),n(E),f(()=>n(i).find(O=>O.name===n(E).name))));(function(O,_){st(_,!1);let b=w(_,"config",12),ue=w(_,"mcpTool",8);const V=Er(),x=da();async function te(){if(n(ae))return Te&&(clearTimeout(Te),Te=null),void m(ae,!1);x.startRemoteMCPAuth(b().name),m(ae,!0);const ye=new Promise(je=>{Te=setTimeout(()=>{je(),Te=null},6e4)});await Promise.race([ye]),m(ae,!1)}async function H(){await x.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${b().displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(ue()&&V.deleteServer(ue()),m(ae,!1))}let ae=j(!1),he=j(!1),Te=null;ve(()=>(C(b()),Vr),()=>{b(Vr(b()))}),ve(()=>C(ue()),()=>{b(b().isConfigured=!!ue(),!0)}),dt(),nt();var Me=Vo(),F=h(Me);Lt(F,{get icon(){return C(b()),f(()=>b().icon)},get title(){return C(b()),f(()=>b().displayName)},$$slots:{"header-right":(ye,je)=>{var se=Do(),Y=h(se),Oe=Re=>{const we=xe(()=>n(ae)?"neutral":"accent");Qe(Re,{variant:"ghost-block",get color(){return n(we)},size:1,$$events:{click:te},children:(Se,qe)=>{var M=Fo(),z=h(M),R=Q=>{var X=Po(),W=$e(X),ne=h(W);tr(ne,{size:1,useCurrentColor:!0}),l(Q,X)},re=Q=>{var X=Zo();l(Q,X)};U(z,Q=>{n(ae)?Q(R):Q(re,!1)}),l(Se,M)},$$slots:{default:!0}})},Ae=(Re,we)=>{var Se=qe=>{var M=jo(),z=h(M);let R;var re=h(z);const Q=xe(()=>(C(hs),f(()=>[hs.Hover])));jt(re,{get triggerOn(){return n(Q)},content:"Revoke Access",children:(W,ne)=>{as(W,{color:"neutral",variant:"ghost",size:1,$$events:{click:H},children:(J,le)=>{ta(J,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var X=v(z,2);br.Root(X,{color:"success",size:1,variant:"soft",children:(W,ne)=>{var J=N("Connected");l(W,J)},$$slots:{default:!0}}),fe(W=>R=At(z,1,"disconnect-button svelte-e3a21z",null,R,W),[()=>({active:n(he)})],xe),l(qe,M)};U(Re,qe=>{C(b()),f(()=>b().isConfigured)&&qe(Se)},we)};U(Y,Re=>{C(b()),f(()=>!b().isConfigured)?Re(Oe):Re(Ae,!1)}),l(ye,se)}}});var pe=v(F,2),Ne=ye=>{var je=Uo(),se=h(je);fe(()=>{At(je,1,`status-message ${C(b()),f(()=>b().statusType)??""}`,"svelte-e3a21z"),ze(se,(C(b()),f(()=>b().statusMessage)))}),l(ye,je)};U(pe,ye=>{C(b()),f(()=>b().showStatus)&&ye(Ne)}),Ct("mouseenter",Me,()=>m(he,!0)),Ct("mouseleave",Me,()=>m(he,!1)),l(O,Me),rt()})(A,{get mcpTool(){return n(K)},get config(){return n(E)}})}),l(G,y)},$$slots:{default:!0}}),l(r,k),rt(),s()}var Go=g('<tr class="env-var-row svelte-1mazg1z"><td class="name-cell svelte-1mazg1z"><!></td><td class="value-cell svelte-1mazg1z"><!></td><td class="action-cell svelte-1mazg1z"><!></td></tr>'),Jo=g('<!> <table class="env-vars-table svelte-1mazg1z"><tbody><!></tbody></table> <div class="new-var-button-container svelte-1mazg1z"><!></div>',1);function Wo(r,e){st(e,!1);let t=w(e,"handleEnterEditMode",8),s=w(e,"envVarEntries",28,()=>[]);nt();var a=Jo(),o=$e(a);ce(o,{size:1,weight:"medium",children:($,de)=>{var me=N("Environment Variables");l($,me)},$$slots:{default:!0}});var i=v(o,2),d=h(i),c=h(d),u=$=>{var de=lt(),me=$e(de);pt(me,1,s,Z=>Z.id,(Z,k,I)=>{var q=Go(),G=h(q),T=h(G);ts(T,{size:1,placeholder:"Name",class:"full-width",get value(){return n(k).key},set value(E){n(k).key=E,Fr(()=>s())},$$events:{focus(...E){var K;(K=t())==null||K.apply(this,E)},change:()=>function(E,K){const O=s().findIndex(_=>_.id===E);O!==-1&&(s(s()[O].key=K,!0),s(s()))}(n(k).id,n(k).key)},$$legacy:!0});var y=v(G),L=h(y);ts(L,{size:1,placeholder:"Value",class:"full-width",get value(){return n(k).value},set value(E){n(k).value=E,Fr(()=>s())},$$events:{focus(...E){var K;(K=t())==null||K.apply(this,E)},change:()=>function(E,K){const O=s().findIndex(_=>_.id===E);O!==-1&&(s(s()[O].value=K,!0),s(s()))}(n(k).id,n(k).value)},$$legacy:!0});var P=v(y),A=h(P);jt(A,{content:"Remove",children:(E,K)=>{Qe(E,{variant:"ghost",color:"neutral",type:"button",size:1,$$events:{focus(...O){var _;(_=t())==null||_.apply(this,O)},click:()=>{return O=n(k).id,t()(),void s(s().filter(_=>_.id!==O));var O}},$$slots:{iconLeft:(O,_)=>{ia(O,{slot:"iconLeft"})}}})},$$slots:{default:!0}}),l(Z,q)}),l($,de)};U(c,$=>{C(s()),f(()=>s().length>0)&&$(u)});var p=v(i,2),oe=h(p);Qe(oe,{size:1,variant:"soft",color:"neutral",type:"button",$$events:{click:function(){t()(),s([...s(),{id:crypto.randomUUID(),key:"",value:""}])}},children:($,de)=>{var me=N("Variable");l($,me)},$$slots:{default:!0,iconLeft:($,de)=>{gs($,{slot:"iconLeft"})}}}),l(r,a),rt()}var Ko=g("<div></div>"),Yo=g(" <!>",1),Qo=g('<div class="server-name svelte-1koxb3z"><!></div>'),Xo=g('<div slot="header-left" class="l-header svelte-1koxb3z"><!> <!> <!> <div class="command-text svelte-1koxb3z"><!></div></div>'),el=g('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),tl=g('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),sl=g('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),rl=g("<!> <!> <!>",1),al=g("<!> <!>",1),nl=g('<div class="server-actions svelte-1koxb3z" slot="header-right"><!> <div class="status-controls svelte-1koxb3z"><!> <!></div></div>'),il=g('<div class="c-tool-item svelte-1koxb3z"><div class="c-tool-info svelte-1koxb3z"><div class="tool-status svelte-1koxb3z"><div></div> <!></div> <div class="c-tool-description svelte-1koxb3z"><!></div></div></div>'),ol=g('<div slot="footer"></div>'),ll=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>',1),dl=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!> <div class="connection-type-buttons svelte-1koxb3z"><!> <!></div></div></div>'),cl=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),ul=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),hl=g('<!> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <!>',1),pl=g('<form><div class="server-edit-form svelte-1koxb3z"><div class="server-header svelte-1koxb3z"><div class="server-title svelte-1koxb3z"><div class="server-icon svelte-1koxb3z"><!></div> <!></div></div> <!> <!> <div class="form-actions-row svelte-1koxb3z"><div><!></div> <div class="form-actions svelte-1koxb3z"><!> <!></div></div></div></form>');function sa(r,e){var je;st(e,!1);const t=j(),s=j(),a=j(),o=j(),i=j(),d=j(),c=j(),u=j();let p=w(e,"server",8,null),oe=w(e,"onDelete",8),$=w(e,"onAdd",8),de=w(e,"onSave",8),me=w(e,"onEdit",8),Z=w(e,"onToggleDisableServer",8),k=w(e,"onJSONImport",8),I=w(e,"onCancel",8),q=w(e,"onAuthenticate",24,()=>{}),G=w(e,"disabledText",24,()=>{}),T=w(e,"warningText",24,()=>{}),y=w(e,"mode",12,"view"),L=w(e,"mcpServerError",12,""),P=j(0),A=j(((je=p())==null?void 0:je.name)??""),E=j(xt(p())?"":us(p())?p().command:""),K=j(xt(p())?p().url:""),O=us(p())?p().env??{}:{},_=j(""),b=j(xt(p())?p().type:"http"),ue=j([]);x();let V=j(!0);function x(){m(ue,Object.entries(O).map(([se,Y])=>({id:crypto.randomUUID(),key:se,value:Y})))}let te=j(()=>{});function H(){p()&&y()==="view"&&(y("edit"),me()(p()),n(te)())}let ae=w(e,"busy",12,!1);function he({key:se,value:Y}){return se.trim()&&Y.trim()}async function Te(){L(""),ae(!0);const se=n(ue).filter(he);O=Object.fromEntries(se.map(({key:Y,value:Oe})=>[Y.trim(),Oe.trim()])),x();try{if(y()==="add"){const Y={type:"stdio",name:n(A).trim(),command:n(E).trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(O).length>0?O:void 0};await $()(Y)}else if(y()==="addRemote"){const Y={type:n(b),name:n(A).trim(),url:n(K).trim()};await $()(Y)}else if(y()==="addJson"){try{JSON.parse(n(_))}catch(Y){const Oe=Y instanceof Error?Y.message:String(Y);throw new ft(`Invalid JSON format: ${Oe}`)}await k()(n(_))}else if(y()==="edit"&&p()){if(xt(p())){const Y={...p(),type:n(b),name:n(A).trim(),url:n(K).trim()};await de()(Y)}else if(us(p())){const Y={...p(),name:n(A).trim(),command:n(E).trim(),arguments:"",env:Object.keys(O).length>0?O:void 0};await de()(Y)}}}catch(Y){L(Y instanceof ft?Y.message:"Failed to save server"),console.warn(Y)}finally{ae(!1)}}function Me(){var se,Y;ae(!1),L(""),(se=I())==null||se(),m(_,""),m(A,((Y=p())==null?void 0:Y.name)??""),m(E,xt(p())?"":us(p())?p().command:""),m(K,xt(p())?p().url:""),O=us(p())&&p().env?{...p().env}:{},m(b,xt(p())?p().type:"http"),x()}ve(()=>C(p()),()=>{var se;m(t,((se=p())==null?void 0:se.tools)??[])}),ve(()=>C(p()),()=>{m(s,xt(p()))}),ve(()=>n(P),()=>{m(a,Date.now()-n(P)<3e3)}),ve(()=>(C(p()),n(s),n(t),n(a)),()=>{var se;m(o,!((se=p())!=null&&se.disabled)&&n(s)&&p().authRequired===!0&&n(t).length===0&&!n(a))}),ve(()=>(n(A),n(E)),()=>{n(A)&&n(E)&&L("")}),ve(()=>(C(y()),n(A),n(E),n(K)),()=>{m(i,!((y()!=="add"||n(A).trim()&&n(E).trim())&&(y()!=="addRemote"||n(A).trim()&&n(K).trim())))}),ve(()=>(C(y()),n(_)),()=>{m(d,y()==="addJson"&&!n(_).trim())}),ve(()=>(n(i),C(y()),n(d)),()=>{m(c,n(i)||y()==="view"||n(d))}),ve(()=>C(y()),()=>{m(u,(()=>{switch(y()){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())}),dt(),nt();var F=lt();fe(()=>{console.log({server:f(()=>Hr(p())),serverTools:f(()=>Hr(n(t)))})});var pe=$e(F),Ne=se=>{ha(se,{get collapsed(){return n(V)},set collapsed(Y){m(V,Y)},$$slots:{header:(Y,Oe)=>{Lt(Y,{slot:"header",$$slots:{"header-left":(Ae,Re)=>{var we=Xo(),Se=h(we),qe=W=>{as(W,{size:1,variant:"ghost",$$events:{click:()=>m(V,!n(V))},children:(ne,J)=>{var le=lt(),De=$e(le),ee=ke=>{on(ke,{})},ge=ke=>{zr(ke,{})};U(De,ke=>{n(V)?ke(ee):ke(ge,!1)}),l(ne,le)},$$slots:{default:!0}})};U(Se,W=>{n(t),f(()=>n(t).length>0)&&W(qe)});var M=v(Se,2);const z=xe(()=>G()||(n(o)?"Authentication required":T()));jt(M,{get content(){return n(z)},children:(W,ne)=>{var J=Ko();let le;fe(De=>le=At(J,1,"c-dot svelte-1koxb3z",null,le,De),[()=>({"c-green":!G()&&!n(o),"c-warning":n(o)||!G()&&!!T(),"c-red":!!G()&&!n(o),"c-disabled":p().disabled})],xe),l(W,J)},$$slots:{default:!0}});var R=v(M,2);jt(R,{get content(){return C(p()),f(()=>p().name)},side:"top",align:"start",children:(W,ne)=>{var J=Qo(),le=h(J);ce(le,{size:1,weight:"medium",children:(De,ee)=>{var ge=Yo(),ke=$e(ge),Pe=v(ke),Ue=We=>{var Ee=N();fe(()=>ze(Ee,`(${n(t),f(()=>n(t).length)??""}) tools`)),l(We,Ee)};U(Pe,We=>{n(t),f(()=>n(t).length>0)&&We(Ue)}),fe(()=>ze(ke,`${C(p()),f(()=>p().name)??""} `)),l(De,ge)},$$slots:{default:!0}}),l(W,J)},$$slots:{default:!0}});var re=v(R,2),Q=h(re);const X=xe(()=>(C(Ys),C(p()),f(()=>Ys(p()))));jt(Q,{get content(){return n(X)},side:"top",align:"start",children:(W,ne)=>{ce(W,{color:"secondary",size:1,weight:"regular",children:(J,le)=>{var De=N();fe(ee=>ze(De,ee),[()=>(C(Ys),C(p()),f(()=>Ys(p())))],xe),l(J,De)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(Ae,we)},"header-right":(Ae,Re)=>{var we=nl(),Se=h(we),qe=Q=>{Qe(Q,{size:1,variant:"surface",color:"warning",$$events:{click:()=>{var X;(X=q())==null||X(p())}},children:(X,W)=>{var ne=N("Authenticate");l(X,ne)},$$slots:{default:!0}})};U(Se,Q=>{n(o)&&Q(qe)});var M=v(Se,2),z=h(M),R=Q=>{const X=xe(()=>(C(p()),f(()=>!p().disabled)));$r(Q,{size:1,get checked(){return n(X)},$$events:{change:()=>{p()&&(m(P,Date.now()),Z()(p().id)),n(te)()}}})};U(z,Q=>{C(Dr),f(Dr)&&Q(R)});var re=v(z,2);tt.Root(re,{get requestClose(){return n(te)},set requestClose(Q){m(te,Q)},children:(Q,X)=>{var W=al(),ne=$e(W);tt.Trigger(ne,{children:(le,De)=>{as(le,{size:1,variant:"ghost-block",color:"neutral",children:(ee,ge)=>{Cn(ee,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var J=v(ne,2);tt.Content(J,{side:"bottom",align:"end",children:(le,De)=>{var ee=rl(),ge=$e(ee);tt.Item(ge,{onSelect:H,children:(Ue,We)=>{var Ee=el(),be=h(Ee);wn(be,{});var Be=v(be,2);ce(Be,{size:1,weight:"medium",children:(Ke,$t)=>{var ot=N("Edit");l(Ke,ot)},$$slots:{default:!0}}),l(Ue,Ee)},$$slots:{default:!0}});var ke=v(ge,2);tt.Item(ke,{onSelect:()=>{(function(){if(p()){const Ue=Gs.convertServerToJSON(p());navigator.clipboard.writeText(Ue)}})(),n(te)()},children:(Ue,We)=>{var Ee=tl(),be=h(Ee);$n(be,{});var Be=v(be,2);ce(Be,{size:1,weight:"medium",children:(Ke,$t)=>{var ot=N("Copy JSON");l(Ke,ot)},$$slots:{default:!0}}),l(Ue,Ee)},$$slots:{default:!0}});var Pe=v(ke,2);tt.Item(Pe,{color:"error",onSelect:()=>{oe()(p()),n(te)()},children:(Ue,We)=>{var Ee=sl(),be=h(Ee);ia(be,{});var Be=v(be,2);ce(Be,{size:1,weight:"medium",children:(Ke,$t)=>{var ot=N("Delete");l(Ke,ot)},$$slots:{default:!0}}),l(Ue,Ee)},$$slots:{default:!0}}),l(le,ee)},$$slots:{default:!0}}),l(Q,W)},$$slots:{default:!0},$$legacy:!0}),l(Ae,we)}}})},footer:(Y,Oe)=>{var Ae=ol();pt(Ae,5,()=>n(t),Bt,(Re,we)=>{var Se=il(),qe=h(Se),M=h(qe),z=h(M);let R;var re=v(z,2);ce(re,{size:1,weight:"medium",children:(W,ne)=>{var J=N();fe(()=>ze(J,(n(we),f(()=>n(we).definition.mcp_tool_name||n(we).definition.name)))),l(W,J)},$$slots:{default:!0}});var Q=v(M,2),X=h(Q);jt(X,{get content(){return n(we),f(()=>n(we).definition.description)},align:"start",children:(W,ne)=>{var J=lt(),le=$e(J),De=ee=>{ce(ee,{size:1,color:"secondary",children:(ge,ke)=>{var Pe=N();fe(()=>ze(Pe,(n(we),f(()=>n(we).definition.description)))),l(ge,Pe)},$$slots:{default:!0}})};U(le,ee=>{n(we),f(()=>n(we).definition.description)&&ee(De)}),l(W,J)},$$slots:{default:!0}}),fe(W=>R=At(z,1,"tool-status-dot svelte-1koxb3z",null,R,W),[()=>({enabled:n(we).enabled,disabled:!n(we).enabled})],xe),l(Re,Se)}),l(Y,Ae)}},$$legacy:!0})},ye=se=>{var Y=pl(),Oe=h(Y),Ae=h(Oe),Re=h(Ae),we=h(Re),Se=h(we);Qa(Se);var qe=v(we,2);ce(qe,{color:"secondary",size:1,weight:"medium",children:(ge,ke)=>{var Pe=N();fe(()=>ze(Pe,n(u))),l(ge,Pe)},$$slots:{default:!0}});var M=v(Ae,2),z=ge=>{var ke=ll(),Pe=$e(ke),Ue=h(Pe),We=h(Ue);ce(We,{size:1,weight:"medium",children:(Ke,$t)=>{var ot=N("Code Snippet");l(Ke,ot)},$$slots:{default:!0}});var Ee=v(Pe,2),be=h(Ee),Be=h(be);va(Be,{size:1,placeholder:"Paste JSON here...",get value(){return n(_)},set value(Ke){m(_,Ke)},$$legacy:!0}),l(ge,ke)},R=(ge,ke)=>{var Pe=Ue=>{var We=hl(),Ee=$e(We),be=ct=>{var Ye=dl(),it=h(Ye),St=h(it);ce(St,{size:1,weight:"medium",children:($s,Or)=>{var Ss=N("Connection Type");l($s,Ss)},$$slots:{default:!0}});var vt=v(St,2),Vt=h(vt);const Fe=xe(()=>n(b)==="http"?"solid":"ghost"),at=xe(()=>n(b)==="http"?"accent":"neutral");Qe(Vt,{size:1,get variant(){return n(Fe)},get color(){return n(at)},type:"button",$$events:{click:()=>m(b,"http")},children:($s,Or)=>{var Ss=N("HTTP");l($s,Ss)},$$slots:{default:!0}});var mt=v(Vt,2);const bs=xe(()=>n(b)==="sse"?"solid":"ghost"),hr=xe(()=>n(b)==="sse"?"accent":"neutral");Qe(mt,{size:1,get variant(){return n(bs)},get color(){return n(hr)},type:"button",$$events:{click:()=>m(b,"sse")},children:($s,Or)=>{var Ss=N("SSE");l($s,Ss)},$$slots:{default:!0}}),l(ct,Ye)};U(Ee,ct=>{C(y()),C(p()),f(()=>{var Ye,it;return y()==="addRemote"||y()==="edit"&&(((Ye=p())==null?void 0:Ye.type)==="http"||((it=p())==null?void 0:it.type)==="sse")})&&ct(be)});var Be=v(Ee,2),Ke=h(Be),$t=h(Ke);ts($t,{size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",get value(){return n(A)},set value(ct){m(A,ct)},$$events:{focus:H},$$slots:{label:(ct,Ye)=>{ce(ct,{slot:"label",size:1,weight:"medium",children:(it,St)=>{var vt=N("Name");l(it,vt)},$$slots:{default:!0}})}},$$legacy:!0});var ot=v(Be,2),Qt=ct=>{var Ye=cl(),it=h(Ye),St=h(it);ts(St,{size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",get value(){return n(K)},set value(vt){m(K,vt)},$$events:{focus:H},$$slots:{label:(vt,Vt)=>{ce(vt,{slot:"label",size:1,weight:"medium",children:(Fe,at)=>{var mt=N("URL");l(Fe,mt)},$$slots:{default:!0}})}},$$legacy:!0}),l(ct,Ye)},ls=ct=>{var Ye=ul(),it=h(Ye),St=h(it);ts(St,{size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",get value(){return n(E)},set value(vt){m(E,vt)},$$events:{focus:H},$$slots:{label:(vt,Vt)=>{ce(vt,{slot:"label",size:1,weight:"medium",children:(Fe,at)=>{var mt=N("Command");l(Fe,mt)},$$slots:{default:!0}})}},$$legacy:!0}),l(ct,Ye)};U(ot,ct=>{C(y()),C(p()),f(()=>{var Ye,it;return y()==="addRemote"||((Ye=p())==null?void 0:Ye.type)==="http"||((it=p())==null?void 0:it.type)==="sse"})?ct(Qt):ct(ls,!1)}),l(Ue,We)};U(ge,Ue=>{y()!=="add"&&y()!=="addRemote"&&y()!=="edit"||Ue(Pe)},ke)};U(M,ge=>{y()==="addJson"?ge(z):ge(R,!1)});var re=v(M,2),Q=ge=>{Wo(ge,{handleEnterEditMode:H,get envVarEntries(){return n(ue)},set envVarEntries(ke){m(ue,ke)},$$legacy:!0})};U(re,ge=>{C(y()),C(xt),C(p()),f(()=>(y()==="add"||y()==="edit")&&!xt(p()))&&ge(Q)});var X=v(re,2),W=h(X);let ne;var J=h(W);Es(J,{variant:"soft",color:"error",size:1,children:(ge,ke)=>{var Pe=N();fe(()=>ze(Pe,L())),l(ge,Pe)},$$slots:{default:!0,icon:(ge,ke)=>{Xa(ge,{slot:"icon"})}}});var le=v(W,2),De=h(le);Qe(De,{size:1,variant:"ghost",color:"neutral",type:"button",$$events:{click:Me},children:(ge,ke)=>{var Pe=N("Cancel");l(ge,Pe)},$$slots:{default:!0}});var ee=v(De,2);Qe(ee,{size:1,variant:"solid",color:"accent",get loading(){return ae()},type:"submit",get disabled(){return n(c)},children:(ge,ke)=>{var Pe=lt(),Ue=$e(Pe),We=be=>{var Be=N("Import");l(be,Be)},Ee=(be,Be)=>{var Ke=ot=>{var Qt=N("Add");l(ot,Qt)},$t=(ot,Qt)=>{var ls=Ye=>{var it=N("Add");l(Ye,it)},ct=(Ye,it)=>{var St=vt=>{var Vt=N("Save");l(vt,Vt)};U(Ye,vt=>{y()==="edit"&&vt(St)},it)};U(ot,Ye=>{y()==="addRemote"?Ye(ls):Ye(ct,!1)},Qt)};U(be,ot=>{y()==="add"?ot(Ke):ot($t,!1)},Be)};U(Ue,be=>{y()==="addJson"?be(We):be(Ee,!1)}),l(ge,Pe)},$$slots:{default:!0}}),fe(ge=>{At(Y,1,"c-mcp-server-card "+(y()==="add"||y()==="addJson"||y()==="addRemote"?"add-server-section":"server-item"),"svelte-1koxb3z"),ne=At(W,1,"error-container svelte-1koxb3z",null,ne,ge)},[()=>({"is-error":!!L()})],xe),Ct("submit",Y,_n(Te)),l(se,Y)};return U(pe,se=>{y()==="view"&&p()?se(Ne):se(ye,!1)}),l(r,F),pa(e,"setLocalEnvVarFormState",x),rt({setLocalEnvVarFormState:x})}var vl=g('<div class="user-input-field svelte-8tbe79"><!> <!> <!></div>'),ml=g('<div class="user-input-container svelte-8tbe79"><!> <div class="user-input-actions svelte-8tbe79"><!> <!></div></div>'),gl=g('<div slot="header-left" class="mcp-service-info svelte-8tbe79"><div class="mcp-service-title svelte-8tbe79"><!></div> <!> <!></div>'),fl=g('<div class="installed-indicator svelte-8tbe79"><!></div>'),yl=g('<div slot="header-right" class="mcp-service-actions svelte-8tbe79"><!></div>'),_l=g('<div class="mcp-service-item"><!></div>'),Cl=g('<div class="mcp-install-content svelte-8tbe79"><div class="mcp-list-container svelte-8tbe79"></div></div>'),wl=g('<div slot="header-left" class="mcp-install-left svelte-8tbe79"><!> <!></div>'),bl=g('<div slot="header" class="mcp-install-header svelte-8tbe79"><!></div>'),$l=g('<div class="mcp-install-wrapper svelte-8tbe79"><!></div>');function Sl(r,e){st(e,!1);let t=w(e,"onMCPServerAdd",24,()=>{}),s=w(e,"servers",24,()=>[]);const a=[{label:mr.REDIS,description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{label:mr.MONGODB,description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{label:mr.CIRCLECI,description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],o="easyMCPInstall.collapsed";let i=j(!1),d=j(!1),c=j(null),u=j({}),p=j({});function oe(Z){var G;if(!Z.userInput)return;for(let T=0;T<Z.userInput.length;T++){const y=Z.userInput[T];let L;if(L=y.type==="environmentVariable"&&y.envVarName?y.envVarName:y.correspondingArg?y.correspondingArg:`input_${T}`,!((G=n(u)[L])==null?void 0:G.trim())){const A=n(p)[L];return void(A&&A.focus())}}let k=[Z.command],I={};Z.args&&k.push(...Z.args);for(let T=0;T<Z.userInput.length;T++){const y=Z.userInput[T];let L;L=y.type==="environmentVariable"&&y.envVarName?y.envVarName:y.correspondingArg?y.correspondingArg:`input_${T}`;const P=n(u)[L].trim(),A=`"${P}"`;if(y.type==="environmentVariable"&&y.envVarName)I[y.envVarName]=P;else if(y.correspondingArg){const E=k.indexOf(y.correspondingArg);E!==-1?k.splice(E+1,0,A):k.push(y.correspondingArg,A)}else k.push(A)}const q={type:"stdio",name:Z.label,command:k.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(I).length>0?I:void 0};t()&&t()(q),m(c,null),m(u,{})}function $(){m(c,null),m(u,{})}ve(()=>{},()=>{const Z=localStorage.getItem(o);if(Z!==null)try{m(i,JSON.parse(Z))}catch{localStorage.removeItem(o)}m(d,!0)}),ve(()=>(n(d),n(i)),()=>{typeof window<"u"&&n(d)&&localStorage.setItem(o,JSON.stringify(n(i)))}),dt(),nt();var de=$l(),me=h(de);ha(me,{get collapsed(){return n(i)},set collapsed(Z){m(i,Z)},children:(Z,k)=>{var I=Cl(),q=h(I);pt(q,5,()=>a,Bt,(G,T)=>{var y=_l();Lt(h(y),{$$slots:{"header-left":(L,P)=>{var A=gl(),E=h(A),K=h(E);ce(K,{size:1,weight:"medium",children:(V,x)=>{var te=N();fe(()=>ze(te,(n(T),f(()=>n(T).label)))),l(V,te)},$$slots:{default:!0}});var O=v(E,2),_=V=>{ce(V,{size:1,color:"secondary",children:(x,te)=>{var H=N();fe(()=>ze(H,(n(T),f(()=>n(T).description)))),l(x,H)},$$slots:{default:!0}})};U(O,V=>{n(T),f(()=>n(T).description)&&V(_)});var b=v(O,2),ue=V=>{var x=ml(),te=h(x);pt(te,1,()=>(n(T),f(()=>n(T).userInput)),Bt,(Te,Me,F)=>{var pe=vl();const Ne=xe(()=>(n(Me),f(()=>n(Me).type==="environmentVariable"&&n(Me).envVarName?n(Me).envVarName:n(Me).correspondingArg||`input_${F}`)));var ye=h(pe);ce(ye,{size:1,weight:"medium",color:"neutral",children:(Ae,Re)=>{var we=N();fe(()=>ze(we,(n(Me),f(()=>n(Me).label)))),l(Ae,we)},$$slots:{default:!0}});var je=v(ye,2),se=Ae=>{ce(Ae,{size:1,color:"secondary",children:(Re,we)=>{var Se=N();fe(()=>ze(Se,(n(Me),f(()=>n(Me).description)))),l(Re,Se)},$$slots:{default:!0}})};U(je,Ae=>{n(Me),f(()=>n(Me).description)&&Ae(se)});var Y=v(je,2);const Oe=xe(()=>(n(Me),f(()=>n(Me).placeholder||"")));ts(Y,{get placeholder(){return n(Oe)},size:1,variant:"surface",get value(){return n(u)[n(Ne)]},set value(Ae){pr(u,n(u)[n(Ne)]=Ae)},get textInput(){return n(p)[n(Ne)]},set textInput(Ae){pr(p,n(p)[n(Ne)]=Ae)},$$events:{keydown:Ae=>{Ae.key==="Enter"?oe(n(T)):Ae.key==="Escape"&&$()}},$$legacy:!0}),l(Te,pe)});var H=v(te,2),ae=h(H);Qe(ae,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>oe(n(T))},children:(Te,Me)=>{var F=N("Install");l(Te,F)},$$slots:{default:!0}});var he=v(ae,2);Qe(he,{variant:"ghost-block",color:"neutral",size:1,$$events:{click:$},children:(Te,Me)=>{var F=N("Cancel");l(Te,F)},$$slots:{default:!0}}),l(V,x)};U(b,V=>{n(c),n(T),f(()=>n(c)===n(T).label&&n(T).userInput)&&V(ue)}),l(L,A)},"header-right":(L,P)=>{var A=yl(),E=h(A),K=_=>{var b=fl(),ue=h(b);br.Root(ue,{color:"success",size:1,variant:"soft",children:(V,x)=>{var te=N("Installed");l(V,te)},$$slots:{default:!0}}),l(_,b)},O=(_,b)=>{var ue=V=>{as(V,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>function(x){if(s().some(H=>H.name===x.label))return;if(x.userInput&&x.userInput.length>0)return m(u,{}),x.userInput.forEach((H,ae)=>{let he;he=H.type==="environmentVariable"&&H.envVarName?H.envVarName:H.correspondingArg?H.correspondingArg:`input_${ae}`,pr(u,n(u)[he]=H.defaultValue||"")}),void m(c,x.label);const te={type:"stdio",name:x.label,command:x.command,arguments:"",useShellInterpolation:!0};t()&&t()(te)}(n(T))},children:(x,te)=>{gs(x,{})},$$slots:{default:!0}})};U(_,V=>{n(c),n(T),f(()=>n(c)!==n(T).label)&&V(ue)},b)};U(E,_=>{C(s()),n(T),f(()=>s().some(b=>b.name===n(T).label))?_(K):_(O,!1)}),l(L,A)}}}),l(G,y)}),l(Z,I)},$$slots:{default:!0,header:(Z,k)=>{var I=bl();Lt(h(I),{$$slots:{"header-left":(q,G)=>{var T=wl(),y=h(T);fn(y,{});var L=v(y,2);ce(L,{color:"neutral",size:1,weight:"light",class:"card-title",children:(P,A)=>{var E=N("Easy MCP Installation");l(P,E)},$$slots:{default:!0}}),l(q,T)}}}),l(Z,I)}},$$legacy:!0}),l(r,de),rt()}const kl={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},xl={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},Ml=Ua(),Al=new class{constructor(r){Ze(this,"strings");let e={[vr.vscode]:{},[vr.jetbrains]:xl,[vr.web]:{}};this.strings={...kl,...e[r]}}get(r){return this.strings[r]}}(Ml.clientType);var Tl=g('<div class="section-heading-text">MCP</div>'),Nl=g(`<div class="mcp-servers svelte-1vnq4q3"><div class="section-heading svelte-1vnq4q3"><!></div> <div class="description-text svelte-1vnq4q3">Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a>in the docs</a>.</div> <!> <!></div> <!> <div class="add-mcp-button-container svelte-1vnq4q3"><!> <!> <!></div>`,1),El=g('<div class="section-heading-text">Terminal</div>'),Il=g("<!> <!>",1),Rl=g('<div class="terminal-settings svelte-dndd5n"><!> <div class="shell-selector svelte-dndd5n"><!> <!></div> <div class="startup-script-container svelte-dndd5n"><!> <!></div></div>');function zl(r,e){st(e,!1);const t=j();let s=w(e,"supportedShells",24,()=>[]),a=w(e,"selectedShell",24,()=>{}),o=w(e,"startupScript",28,()=>{}),i=w(e,"onShellSelect",8),d=w(e,"onStartupScriptChange",8),c=j();ve(()=>C(a()),()=>{var I;m(t,a()?(I=a(),s().find(q=>q.friendlyName===I)):void 0)}),dt(),nt();var u=Rl(),p=h(u);ce(p,{size:1,weight:"regular",color:"secondary",children:(I,q)=>{var G=El();l(I,G)},$$slots:{default:!0}});var oe=v(p,2),$=h(oe);ce($,{size:1,children:(I,q)=>{var G=N("Shell:");l(I,G)},$$slots:{default:!0}});var de=v($,2);tt.Root(de,{get requestClose(){return n(c)},set requestClose(I){m(c,I)},children:(I,q)=>{var G=Il(),T=$e(G);tt.Trigger(T,{children:(L,P)=>{const A=xe(()=>(C(s()),f(()=>s().length===0)));Qe(L,{size:1,variant:"outline",color:"neutral",get disabled(){return n(A)},children:(E,K)=>{var O=lt(),_=$e(O),b=V=>{var x=N();fe(()=>ze(x,`${n(t),f(()=>n(t).friendlyName)??""}
            (${n(t),f(()=>n(t).supportString)??""})`)),l(V,x)},ue=(V,x)=>{var te=ae=>{var he=N("No shells available");l(ae,he)},H=ae=>{var he=N("Select a shell");l(ae,he)};U(V,ae=>{C(s()),f(()=>s().length===0)?ae(te):ae(H,!1)},x)};U(_,V=>{n(t),C(s()),f(()=>n(t)&&s().length>0)?V(b):V(ue,!1)}),l(E,O)},$$slots:{default:!0,iconRight:(E,K)=>{tn(E)}}})},$$slots:{default:!0}});var y=v(T,2);tt.Content(y,{side:"bottom",align:"start",children:(L,P)=>{var A=lt(),E=$e(A),K=_=>{var b=lt(),ue=$e(b);pt(ue,1,s,V=>V.friendlyName,(V,x)=>{const te=xe(()=>(C(a()),n(x),f(()=>a()===n(x).friendlyName)));tt.Item(V,{onSelect:()=>{i()(n(x).friendlyName),n(c)()},get highlight(){return n(te)},children:(H,ae)=>{var he=N();fe(()=>ze(he,`${n(x),f(()=>n(x).friendlyName)??""}
              (${n(x),f(()=>n(x).supportString)??""})`)),l(H,he)},$$slots:{default:!0}})}),l(_,b)},O=_=>{tt.Label(_,{children:(b,ue)=>{var V=N("No shells available");l(b,V)},$$slots:{default:!0}})};U(E,_=>{C(s()),f(()=>s().length>0)?_(K):_(O,!1)}),l(L,A)},$$slots:{default:!0}}),l(I,G)},$$slots:{default:!0},$$legacy:!0});var me=v(oe,2),Z=h(me);ce(Z,{size:1,children:(I,q)=>{var G=N("Start-up script: Code to run wherever a new terminal is opened");l(I,G)},$$slots:{default:!0}});var k=v(Z,2);va(k,{placeholder:"Enter shell commands to run on terminal startup",resize:"vertical",get value(){return o()},set value(I){o(I)},$$events:{change:function(I){const q=I.target;d()(q.value)}},$$legacy:!0}),l(r,u),rt()}var Ll=g('<div class="section-heading-text">Sound Settings</div>'),Ol=g('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),Pl=g('<div slot="header-right"><!></div>'),Zl=g('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),Fl=g('<div slot="header-right"><!></div>'),jl=g('<!> <div class="c-sound-settings svelte-8awonv"><!> <!></div>',1),Dl=g('<div class="section-heading-text">Agent Settings</div>'),Ul=g('<div class="c-agent-setting__info svelte-mv39d5" slot="header-left"><div><!></div> <div><!></div> <div class="c-agent-setting__education svelte-mv39d5"><!></div></div>'),Vl=g('<div slot="header-right"><!></div>'),ql=g('<!> <div class="c-agent-settings svelte-mv39d5"><!></div>',1),Hl=g('<div class="c-settings-tools svelte-181yusq"><!> <!> <!> <!> <!></div>');function Bl(r,e){let t=w(e,"tools",24,()=>[]),s=w(e,"isMCPEnabled",8,!0),a=w(e,"isMCPImportEnabled",8,!0),o=w(e,"isTerminalEnabled",8,!0),i=w(e,"isSoundCategoryEnabled",8,!1),d=w(e,"isAgentCategoryEnabled",8,!1),c=w(e,"isSwarmModeFeatureFlagEnabled",8,!1),u=w(e,"hasEverUsedRemoteAgent",8,!1),p=w(e,"onAuthenticate",8),oe=w(e,"onRevokeAccess",8),$=w(e,"onToolApprovalConfigChange",8,()=>{}),de=w(e,"onMCPServerAdd",8),me=w(e,"onMCPServerSave",8),Z=w(e,"onMCPServerDelete",8),k=w(e,"onMCPServerToggleDisable",8),I=w(e,"onMCPServerJSONImport",8),q=w(e,"onCancel",24,()=>{}),G=w(e,"supportedShells",24,()=>[]),T=w(e,"selectedShell",24,()=>{}),y=w(e,"startupScript",24,()=>{}),L=w(e,"onShellSelect",8,()=>{}),P=w(e,"onStartupScriptChange",8,()=>{});var A=Hl(),E=h(A);Bo(E,{title:"Services",get tools(){return t()},get onAuthenticate(){return p()},get onRevokeAccess(){return oe()},onToolApprovalConfigChange:$()});var K=v(E,2),O=H=>{(function(ae,he){st(he,!1);const[Te,Me]=Et(),F=()=>et(W,"$enableNativeRemoteMcp",Te),pe=()=>et(ne,"$allServers",Te),Ne=j(),ye=j();let je=w(he,"onMCPServerAdd",8),se=w(he,"onMCPServerSave",8),Y=w(he,"onMCPServerDelete",8),Oe=w(he,"onMCPServerToggleDisable",8),Ae=w(he,"onCancel",24,()=>{}),Re=w(he,"onMCPServerJSONImport",8),we=w(he,"isMCPImportEnabled",8,!0);const Se=da();function qe(Fe){Se.startRemoteMCPAuth(Fe.name)}let M=j(null),z=j(null);function R(){var Fe;m(M,null),m(z,null),(Fe=Ae())==null||Fe()}let re=j([]);const Q=vs(fs.key),X=Er(),W=Q.getEnableNativeRemoteMcp(),ne=X.getServers();function J(Fe){m(M,Fe.id)}function le(Fe){return async function(...at){const mt=await Fe(...at);return m(z,null),m(M,null),mt}}const De=le(je()),ee=le(se()),ge=le(Re()),ke=le(Y()),Pe=le(Oe()),Ue=Al.get("mcpDocsURL");ve(()=>(n(z),n(M)),()=>{m(Ne,n(z)==="add"||n(z)==="addJson"||n(z)==="addRemote"||n(M)!==null)}),ve(()=>(F(),pe()),()=>{F()?m(re,en(pe())):m(re,pe())}),ve(()=>n(re),()=>{m(ye,Gs.parseServerValidationMessages(n(re)))}),dt(),nt();var We=Nl(),Ee=$e(We),be=h(Ee),Be=h(be);ce(Be,{size:1,weight:"regular",color:"secondary",children:(Fe,at)=>{var mt=Tl();l(Fe,mt)},$$slots:{default:!0}});var Ke=v(be,2),$t=v(h(Ke)),ot=v(Ke,2);Sl(ot,{get onMCPServerAdd(){return De},get servers(){return n(re)}});var Qt=v(ot,2);pt(Qt,1,()=>n(re),Fe=>Fe.id,(Fe,at)=>{const mt=xe(()=>(n(M),n(at),f(()=>n(M)===n(at).id?"edit":"view"))),bs=xe(()=>(n(ye),n(at),f(()=>n(ye).errors.get(n(at).id)))),hr=xe(()=>(n(ye),n(at),f(()=>n(ye).warnings.get(n(at).id))));sa(Fe,{get mode(){return n(mt)},get server(){return n(at)},get onAdd(){return De},get onSave(){return ee},get onDelete(){return ke},get onToggleDisableServer(){return Pe},onEdit:J,onCancel:R,get onJSONImport(){return ge},onAuthenticate:qe,get disabledText(){return n(bs)},get warningText(){return n(hr)}})});var ls=v(Ee,2),ct=Fe=>{sa(Fe,{get mode(){return n(z)},get onAdd(){return De},get onSave(){return ee},get onDelete(){return ke},get onToggleDisableServer(){return Pe},onEdit:J,onCancel:R,get onJSONImport(){return ge},onAuthenticate:qe})};U(ls,Fe=>{n(z)!=="add"&&n(z)!=="addJson"&&n(z)!=="addRemote"||Fe(ct)});var Ye=v(ls,2),it=h(Ye);Qe(it,{get disabled(){return n(Ne)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{m(z,"add")}},children:(Fe,at)=>{var mt=N("Add MCP");l(Fe,mt)},$$slots:{default:!0,iconLeft:(Fe,at)=>{gs(Fe,{slot:"iconLeft"})}}});var St=v(it,2);Qe(St,{get disabled(){return n(Ne)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{m(z,"addRemote")}},children:(Fe,at)=>{var mt=N("Add remote MCP");l(Fe,mt)},$$slots:{default:!0,iconLeft:(Fe,at)=>{gs(Fe,{slot:"iconLeft"})}}});var vt=v(St,2),Vt=Fe=>{Qe(Fe,{get disabled(){return n(Ne)},color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$events:{click:()=>{m(z,"addJson")}},children:(at,mt)=>{var bs=N("Import from JSON");l(at,bs)},$$slots:{default:!0,iconLeft:(at,mt)=>{ca(at,{slot:"iconLeft"})}}})};U(vt,Fe=>{we()&&Fe(Vt)}),fe(()=>es($t,"href",Ue)),l(ae,We),rt(),Me()})(H,{get onMCPServerAdd(){return de()},get onMCPServerSave(){return me()},get onMCPServerDelete(){return Z()},get onMCPServerToggleDisable(){return k()},get onMCPServerJSONImport(){return I()},get onCancel(){return q()},get isMCPImportEnabled(){return a()}})};U(K,H=>{s()&&H(O)});var _=v(K,2),b=H=>{zl(H,{get supportedShells(){return G()},get selectedShell(){return T()},get startupScript(){return y()},onShellSelect:L(),onStartupScriptChange:P()})};U(_,H=>{o()&&H(b)});var ue=v(_,2),V=H=>{(function(ae,he){st(he,!1);const[Te,Me]=Et(),F=()=>et(n(pe),"$currentSettings",Te),pe=j(),Ne=j(),ye=vs(Sr.key);async function je(){return await ye.playAgentComplete(),"success"}ve(()=>{},()=>{Ns(m(pe,ye.getCurrentSettings),"$currentSettings",Te)}),ve(()=>F(),()=>{m(Ne,F().enabled)}),dt(),nt();var se=jl(),Y=$e(se);ce(Y,{size:1,weight:"regular",color:"secondary",children:(Se,qe)=>{var M=Ll();l(Se,M)},$$slots:{default:!0}});var Oe=v(Y,2),Ae=h(Oe);Lt(Ae,{$$slots:{"header-left":(Se,qe)=>{var M=Ol(),z=h(M),R=h(z);ce(R,{size:2,weight:"medium",children:(X,W)=>{var ne=N("Enable Sound Effects");l(X,ne)},$$slots:{default:!0}});var re=v(z,2),Q=h(re);ce(Q,{size:1,weight:"medium",children:(X,W)=>{var ne=N("Play a sound when an agent completes a task");l(X,ne)},$$slots:{default:!0}}),l(Se,M)},"header-right":(Se,qe)=>{var M=Pl(),z=h(M);$r(z,{size:1,get checked(){return n(Ne)},$$events:{change:()=>ye.updateEnabled(!n(Ne))}}),l(Se,M)}}});var Re=v(Ae,2),we=Se=>{Lt(Se,{$$slots:{"header-left":(qe,M)=>{var z=Zl(),R=h(z),re=h(R);ce(re,{size:2,weight:"medium",children:(W,ne)=>{var J=N("Test Sound");l(W,J)},$$slots:{default:!0}});var Q=v(R,2),X=h(Q);ce(X,{size:1,weight:"medium",children:(W,ne)=>{var J=N("Play a sample of the agent completion sound");l(W,J)},$$slots:{default:!0}}),l(qe,z)},"header-right":(qe,M)=>{var z=Fl(),R=h(z);const re=xe(()=>n(Ne)?"":"Enable sound effects to test"),Q=xe(()=>(C(hs),f(()=>[hs.Hover])));jt(R,{get content(){return n(re)},get triggerOn(){return n(Q)},children:(X,W)=>{const ne=xe(()=>!n(Ne));bn(X,{size:1,defaultColor:"neutral",get enabled(){return n(Ne)},stickyColor:!1,get disabled(){return n(ne)},onClick:je,tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},children:(J,le)=>{var De=N("Play");l(J,De)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(qe,z)}}})};U(Re,Se=>{n(Ne)&&Se(we)}),l(ae,se),rt(),Me()})(H,{})};U(ue,H=>{i()&&H(V)});var x=v(ue,2),te=H=>{(function(ae,he){st(he,!1);const[Te,Me]=Et(),F=()=>et(n(pe),"$currentSettings",Te),pe=j(),Ne=j();let ye=w(he,"isSwarmModeEnabled",8,!1),je=w(he,"hasEverUsedRemoteAgent",8,!1);const se=vs(qs.key);ve(()=>{},()=>{Ns(m(pe,se.getCurrentSettings),"$currentSettings",Te)}),ve(()=>F(),()=>{m(Ne,F().enabled)}),dt(),nt();var Y=lt(),Oe=$e(Y),Ae=Re=>{var we=ql(),Se=$e(we);ce(Se,{size:1,weight:"regular",color:"secondary",children:(M,z)=>{var R=Dl();l(M,R)},$$slots:{default:!0}});var qe=v(Se,2);Lt(h(qe),{$$slots:{"header-left":(M,z)=>{var R=Ul(),re=h(R),Q=h(re);ce(Q,{size:2,weight:"medium",children:(le,De)=>{var ee=N("Enable Swarm Mode");l(le,ee)},$$slots:{default:!0}});var X=v(re,2),W=h(X);ce(W,{size:1,weight:"medium",children:(le,De)=>{var ee=N("Allow agents to coordinate and work together on complex tasks");l(le,ee)},$$slots:{default:!0}});var ne=v(X,2),J=h(ne);ce(J,{size:1,weight:"regular",color:"secondary",children:(le,De)=>{var ee=N(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`);l(le,ee)},$$slots:{default:!0}}),l(M,R)},"header-right":(M,z)=>{var R=Vl(),re=h(R);$r(re,{size:1,get checked(){return n(Ne)},$$events:{change:()=>se.updateEnabled(!n(Ne))}}),l(M,R)}}}),l(Re,we)};U(Oe,Re=>{ye()&&je()&&Re(Ae)}),l(ae,Y),rt(),Me()})(H,{get isSwarmModeEnabled(){return c()},get hasEverUsedRemoteAgent(){return u()}})};U(x,H=>{d()&&H(te)}),l(r,A)}var Gl=bt("<svg><!></svg>");function Jl(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=Gl();ws(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 576 512",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',!0),l(r,s)}var Wl=bt("<svg><!></svg>");function Kl(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=Wl();ws(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',!0),l(r,s)}var Yl=bt("<svg><!></svg>");function ra(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=Yl();ws(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',!0),l(r,s)}var Ql=g('<div class="c-user-guidelines-category__input svelte-10borzo"><!></div>');function ka(r,e){st(e,!1);const[t,s]=Et(),a=Rr();let o=w(e,"userGuidelines",12,""),i=w(e,"userGuidelinesLengthLimit",24,()=>{}),d=w(e,"updateUserGuideline",8,()=>!1);const c=ht(void 0);function u(){const $=o().trim();if(et(c,"$originalValue",t)!==$){if(!d()($))throw i()&&$.length>i()?`The user guideline must be less than ${i()} character long`:"An error occurred updating the user";jr(c,$)}}Pa(()=>{jr(c,o().trim())}),aa(()=>{u()}),nt();var p=Ql(),oe=h(p);kn(oe,{placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:u,get value(){return o()},set value($){o($)},$$events:{focus:$=>{a("focus",$)}},$$legacy:!0}),l(r,p),rt(),s()}var Xl=g("<!> <!> <!>",1),ed=g('<div slot="footer"><!> <!></div>'),td=g('<input type="text" value="No existing rules found" readonly="" class="c-dropdown-input svelte-z1s6x7"/>'),sd=g('<div class="c-dropdown-trigger svelte-z1s6x7"><input type="text" readonly="" class="c-dropdown-input svelte-z1s6x7"/> <!></div>'),rd=g("<!> <!>",1),ad=g("<!> <!>",1),nd=g("<!> <!>",1),id=g("<!> <!> <!> <!>",1),od=g('<div slot="body" class="c-auto-import-rules-dialog svelte-z1s6x7"><!></div>'),ld=g('<div slot="footer"><!> <!></div>');function dd(r,e){st(e,!1);const[t,s]=Et(),a=()=>et(n(de),"$focusedIndex",t),o=j(),i=Rr();let d=w(e,"show",8,!1),c=w(e,"options",24,()=>[]),u=w(e,"isLoading",8,!1),p=w(e,"errorMessage",8,""),oe=w(e,"successMessage",8,""),$=j(n(o)),de=j(void 0),me=j(()=>{});function Z(){n($)&&!u()&&i("select",n($))}function k(){u()||(i("cancel"),m($,n(o)))}ve(()=>C(c()),()=>{m(o,c().length>0?c()[0]:null)}),ve(()=>(C(d()),n(o)),()=>{d()&&m($,n(o))}),dt(),nt(),Ct("keydown",Ir,function(I){d()&&!u()&&(I.key==="Escape"?(I.preventDefault(),k()):I.key==="Enter"&&n($)&&(I.preventDefault(),Z()))}),ma(r,{get show(){return d()},title:"Auto Import Rules",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return u()},get preventEscapeClose(){return u()},$$events:{cancel:k},$$slots:{body:(I,q)=>{var G=od(),T=h(G),y=P=>{var A=td();l(P,A)},L=P=>{var A=id(),E=$e(A);ce(E,{size:2,color:"secondary",children:(x,te)=>{var H=N("Select existing rules to auto import to .augment/rules");l(x,H)},$$slots:{default:!0}});var K=v(E,2);const O=xe(()=>(C(c()),f(()=>c().length===0?[]:void 0)));tt.Root(K,{get triggerOn(){return n(O)},get requestClose(){return n(me)},set requestClose(x){m(me,x)},get focusedIndex(){return n(de)},set focusedIndex(x){Ns(m(de,x),"$focusedIndex",t)},children:(x,te)=>{var H=nd(),ae=$e(H);tt.Trigger(ae,{children:(Te,Me)=>{var F=sd(),pe=h(F),Ne=v(pe,2);zr(Ne,{class:"c-dropdown-chevron"}),fe(()=>Za(pe,(n($),f(()=>n($)?n($).label:"Existing rules")))),l(Te,F)},$$slots:{default:!0}});var he=v(ae,2);tt.Content(he,{align:"start",side:"bottom",children:(Te,Me)=>{var F=ad(),pe=$e(F);pt(pe,1,c,Bt,(je,se)=>{const Y=xe(()=>(n($),n(se),f(()=>{var Oe;return((Oe=n($))==null?void 0:Oe.label)===n(se).label})));tt.Item(je,{onSelect:()=>function(Oe){m($,Oe),n(me)()}(n(se)),get highlight(){return n(Y)},children:(Oe,Ae)=>{var Re=N();fe(()=>ze(Re,(n(se),f(()=>n(se).label)))),l(Oe,Re)},$$slots:{default:!0}})});var Ne=v(pe,2),ye=je=>{var se=rd(),Y=$e(se);tt.Separator(Y,{});var Oe=v(Y,2);tt.Label(Oe,{children:(Ae,Re)=>{var we=N();fe(()=>ze(we,(a(),C(c()),n($),f(()=>{var Se;return a()!==void 0?c()[a()].description:(Se=n($))==null?void 0:Se.description})))),l(Ae,we)},$$slots:{default:!0}}),l(je,se)};U(Ne,je=>{(a()!==void 0||n($))&&je(ye)}),l(Te,F)},$$slots:{default:!0}}),l(x,H)},$$slots:{default:!0},$$legacy:!0});var _=v(K,2),b=x=>{Es(x,{variant:"soft",color:"error",size:1,children:(te,H)=>{var ae=N();fe(()=>ze(ae,p())),l(te,ae)},$$slots:{default:!0,icon:(te,H)=>{Xs(te,{slot:"icon"})}}})};U(_,x=>{p()&&x(b)});var ue=v(_,2),V=x=>{Es(x,{variant:"soft",color:"success",size:1,children:(te,H)=>{var ae=N();fe(()=>ze(ae,oe())),l(te,ae)},$$slots:{default:!0,icon:(te,H)=>{sn(te,{slot:"icon"})}}})};U(ue,x=>{oe()&&x(V)}),l(P,A)};U(T,P=>{C(c()),f(()=>c().length===0)?P(y):P(L,!1)}),l(I,G)},footer:(I,q)=>{var G=ld(),T=h(G);Qe(T,{variant:"solid",color:"neutral",get disabled(){return u()},$$events:{click:k},children:(P,A)=>{var E=N("Cancel");l(P,E)},$$slots:{default:!0}});var y=v(T,2),L=P=>{const A=xe(()=>!n($)||u());Qe(P,{color:"accent",variant:"solid",get disabled(){return n(A)},get loading(){return u()},$$events:{click:Z},children:(E,K)=>{var O=N();fe(()=>ze(O,u()?"Importing...":"Import ")),l(E,O)},$$slots:{default:!0}})};U(y,P=>{C(c()),f(()=>c().length>0)&&P(L)}),l(I,G)}}}),rt(),s()}var cd=g('<div class="loading-container"><!> <!></div>'),ud=g('<div class="c-rules-list-empty svelte-mrq2l0"><!></div>'),hd=g('<div class="c-rule-item-info svelte-mrq2l0" slot="header-left"><div class="l-icon-wrapper svelte-mrq2l0"><!></div> <div class="c-rule-item-path svelte-mrq2l0"><!></div></div>'),pd=g('<div class="server-actions" slot="header-right"><div class="status-controls svelte-mrq2l0"><div class="c-rules-dropdown"><!></div> <!> <!></div></div>'),vd=g('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Create new rule file</div>'),md=g('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Import rules <!></div>'),gd=g("<!> <!>",1),fd=g("<!> <!>",1),yd=g("<!> <!>",1),_d=g(`<div class="c-rules-category svelte-mrq2l0"><div class="c-rules-section svelte-mrq2l0"><!> <div>Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!> <div class="c-rules-list svelte-mrq2l0"><!></div> <div class="c-rules-actions-container svelte-mrq2l0"><!> <!></div></div> <div class="c-user-guidelines-section svelte-mrq2l0"><!> <div>User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!></div></div> <!> <!>`,1);function Cd(r,e){st(e,!1);const[t,s]=Et(),a=()=>et(q,"$rulesFiles",t),o=()=>et(n(d),"$isRulesLoading",t),i=()=>et(n(O),"$importFocusedIndex",t),d=j(),c=j(),u=j(),p=j();let oe=w(e,"userGuidelines",8,""),$=w(e,"userGuidelinesLengthLimit",24,()=>{}),de=w(e,"workspaceGuidelinesLengthLimit",24,()=>{}),me=w(e,"workspaceGuidelinesContent",8,""),Z=w(e,"updateUserGuideline",8,()=>!1),k=w(e,"rulesModel",8),I=w(e,"rulesController",8);const q=k().getCachedRules(),G=I().getShowCreateRuleDialog(),T=I().getCreateRuleError();let y=j(!1),L=j([]),P=j(!1),A=j(""),E=j("");const K=[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}];let O=j(void 0),_=j(()=>{});async function b(M){try{M.id==="select_file_or_directory"?await I().selectFileToImport():M.id==="auto_import"&&await async function(){try{m(A,""),m(E,"");const z=await I().getAutoImportOptions();m(L,z.data.options),m(y,!0)}catch(z){console.error("Failed to get auto-import options:",z),m(A,"Failed to detect existing rules in workspace.")}}()}catch(z){console.error("Failed to handle import select:",z)}n(_)&&n(_)()}ve(()=>C(k()),()=>{Ns(m(d,k().getLoading()),"$isRulesLoading",t)}),ve(()=>C(de()),()=>{m(c,de())}),ve(()=>(n(u),n(p),a(),C(me()),n(c)),()=>{var M;M=pn({rules:a(),workspaceGuidelinesContent:me(),rulesAndGuidelinesLimit:n(c)}),m(u,M.isOverLimit),m(p,M.warningMessage)}),dt(),nt();var ue=_d(),V=$e(ue),x=h(V),te=h(x);ce(te,{class:"c-section-header",size:3,color:"primary",children:(M,z)=>{var R=N("Rules");l(M,R)},$$slots:{default:!0}});var H=v(te,2),ae=v(h(H)),he=h(ae);ce(he,{size:1,weight:"regular",children:(M,z)=>{var R=N("Learn more");l(M,R)},$$slots:{default:!0}});var Te=v(H,2),Me=M=>{Es(M,{variant:"soft",color:"warning",size:1,children:(z,R)=>{var re=N();fe(()=>ze(re,n(p))),l(z,re)},$$slots:{default:!0,icon:(z,R)=>{Xs(z,{slot:"icon"})}}})};U(Te,M=>{n(u)&&M(Me)});var F=v(Te,2),pe=h(F),Ne=M=>{var z=cd(),R=h(z);tr(R,{size:1});var re=v(R,2);ce(re,{size:1,color:"secondary",children:(Q,X)=>{var W=N("Loading rules...");l(Q,W)},$$slots:{default:!0}}),l(M,z)},ye=(M,z)=>{var R=Q=>{var X=ud(),W=h(X);ce(W,{size:1,color:"neutral",children:(ne,J)=>{var le=N("No rules files found");l(ne,le)},$$slots:{default:!0}}),l(Q,X)},re=Q=>{var X=lt(),W=$e(X);pt(W,1,a,ne=>ne.path,(ne,J)=>{Lt(ne,{isClickable:!0,$$events:{click:()=>I().openRule(n(J).path)},$$slots:{"header-left":(le,De)=>{var ee=hd(),ge=h(ee),ke=h(ge),Pe=be=>{jt(be,{content:"No description found",children:(Be,Ke)=>{Xs(Be,{})},$$slots:{default:!0}})},Ue=be=>{nn(be,{})};U(ke,be=>{n(J),C(Ur),f(()=>n(J).type===Ur.AGENT_REQUESTED&&!n(J).description)?be(Pe):be(Ue,!1)});var We=v(ge,2),Ee=h(We);ce(Ee,{size:1,color:"neutral",children:(be,Be)=>{var Ke=N();fe(()=>ze(Ke,(n(J),f(()=>n(J).path)))),l(be,Ke)},$$slots:{default:!0}}),l(le,ee)},"header-right":(le,De)=>{var ee=pd(),ge=h(ee),ke=h(ge),Pe=h(ke);xn(Pe,{get rule(){return n(J)},onSave:async(Ee,be)=>{await k().updateRuleContent({type:Ee,path:n(J).path,content:n(J).content,description:be})}});var Ue=v(ke,2);Qe(Ue,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ee=>{Ee.stopPropagation(),I().openRule(n(J).path)}},$$slots:{iconRight:(Ee,be)=>{rn(Ee,{slot:"iconRight"})}}});var We=v(Ue,2);Qe(We,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ee=>{Ee.stopPropagation(),I().deleteRule(n(J).path)}},$$slots:{iconRight:(Ee,be)=>{an(Ee,{slot:"iconRight"})}}}),l(le,ee)}}})}),l(Q,X)};U(M,Q=>{a(),f(()=>a().length===0)?Q(R):Q(re,!1)},z)};U(pe,M=>{o(),a(),f(()=>o()&&a().length===0)?M(Ne):M(ye,!1)});var je=v(F,2),se=h(je);Qe(se,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$events:{click:()=>I().createRule()},children:(M,z)=>{var R=vd(),re=h(R);gs(re,{slot:"iconLeft"}),l(M,R)},$$slots:{default:!0}});var Y=v(se,2);tt.Root(Y,{get requestClose(){return n(_)},set requestClose(M){m(_,M)},get focusedIndex(){return n(O)},set focusedIndex(M){Ns(m(O,M),"$importFocusedIndex",t)},children:(M,z)=>{var R=yd(),re=$e(R);tt.Trigger(re,{children:(X,W)=>{Qe(X,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",children:(ne,J)=>{var le=md(),De=h(le);ca(De,{slot:"iconLeft"});var ee=v(De,2);zr(ee,{slot:"iconRight"}),l(ne,le)},$$slots:{default:!0}})},$$slots:{default:!0}});var Q=v(re,2);tt.Content(Q,{align:"start",side:"bottom",children:(X,W)=>{var ne=fd(),J=$e(ne);pt(J,1,()=>K,ee=>ee.id,(ee,ge)=>{tt.Item(ee,{onSelect:()=>b(n(ge)),children:(ke,Pe)=>{var Ue=N();fe(()=>ze(Ue,(n(ge),f(()=>n(ge).label)))),l(ke,Ue)},$$slots:{default:!0}})});var le=v(J,2),De=ee=>{var ge=gd(),ke=$e(ge);tt.Separator(ke,{});var Pe=v(ke,2);tt.Label(Pe,{children:(Ue,We)=>{var Ee=N();fe(()=>ze(Ee,(i(),f(()=>i()!==void 0?K[i()].description:K[0])))),l(Ue,Ee)},$$slots:{default:!0}}),l(ee,ge)};U(le,ee=>{i()!==void 0&&ee(De)}),l(X,ne)},$$slots:{default:!0}}),l(M,R)},$$slots:{default:!0},$$legacy:!0});var Oe=v(x,2),Ae=h(Oe);ce(Ae,{class:"c-section-header",size:3,color:"primary",children:(M,z)=>{var R=N("User Guidelines");l(M,R)},$$slots:{default:!0}});var Re=v(Ae,2),we=v(h(Re)),Se=h(we);ce(Se,{size:1,weight:"regular",children:(M,z)=>{var R=N("Learn more");l(M,R)},$$slots:{default:!0}}),ka(v(Re,2),{get userGuidelines(){return oe()},get userGuidelinesLengthLimit(){return $()},updateUserGuideline:Z()});var qe=v(V,2);(function(M,z){st(z,!1);const R=Rr();let re=w(z,"show",8,!1),Q=w(z,"errorMessage",8,""),X=j(""),W=j(void 0),ne=j(!1);function J(){n(X).trim()&&!n(ne)&&(m(ne,!0),R("create",n(X).trim()))}function le(){n(ne)||(R("cancel"),m(X,""))}function De(ee){n(ne)||(ee.key==="Enter"?(ee.preventDefault(),J()):ee.key==="Escape"&&(ee.preventDefault(),le()))}ve(()=>(C(re()),n(W)),()=>{re()&&n(W)&&setTimeout(()=>{var ee;return(ee=n(W))==null?void 0:ee.focus()},100)}),ve(()=>(C(re()),C(Q())),()=>{re()&&!Q()||m(ne,!1)}),ve(()=>(C(re()),C(Q())),()=>{re()||Q()||m(X,"")}),dt(),nt(),ma(M,{get show(){return re()},title:"Create New Rule",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return n(ne)},get preventEscapeClose(){return n(ne)},$$events:{cancel:le,keydown:function(ee){n(ne)||ee.detail.key==="Enter"&&(ee.detail.preventDefault(),J())}},$$slots:{body:(ee,ge)=>{var ke=Xl(),Pe=$e(ke);ce(Pe,{size:2,color:"secondary",children:(be,Be)=>{var Ke=N("Enter a name for the new rule file (e.g., architecture.md):");l(be,Ke)},$$slots:{default:!0}});var Ue=v(Pe,2);ts(Ue,{placeholder:"rule-name.md",get disabled(){return n(ne)},get value(){return n(X)},set value(be){m(X,be)},get textInput(){return n(W)},set textInput(be){m(W,be)},$$events:{keydown:De},$$legacy:!0});var We=v(Ue,2),Ee=be=>{Es(be,{variant:"soft",color:"error",size:1,children:(Be,Ke)=>{var $t=N();fe(()=>ze($t,Q())),l(Be,$t)},$$slots:{default:!0,icon:(Be,Ke)=>{Xs(Be,{slot:"icon"})}}})};U(We,be=>{Q()&&be(Ee)}),l(ee,ke)},footer:(ee,ge)=>{var ke=ed(),Pe=h(ke);Qe(Pe,{variant:"solid",color:"neutral",get disabled(){return n(ne)},$$events:{click:le},children:(Ee,be)=>{var Be=N("Cancel");l(Ee,Be)},$$slots:{default:!0}});var Ue=v(Pe,2);const We=xe(()=>(n(X),n(ne),f(()=>!n(X).trim()||n(ne))));Qe(Ue,{variant:"solid",color:"accent",get disabled(){return n(We)},get loading(){return n(ne)},$$events:{click:J},children:(Ee,be)=>{var Be=N();fe(()=>ze(Be,n(ne)?"Creating...":"Create")),l(Ee,Be)},$$slots:{default:!0}}),l(ee,ke)}}}),rt()})(qe,{get show(){return et(G,"$showCreateRuleDialog",t)},get errorMessage(){return et(T,"$createRuleError",t)},$$events:{create:function(M){I().handleCreateRuleWithName(M.detail)},cancel:function(){I().hideCreateRuleDialog()}}}),dd(v(qe,2),{get show(){return n(y)},get options(){return n(L)},get isLoading(){return n(P)},get errorMessage(){return n(A)},get successMessage(){return n(E)},$$events:{select:async function(M){const z=M.detail;try{m(P,!0),m(A,"");const R=await I().processAutoImportSelection(z);let re=`Successfully imported ${R.importedRulesCount} rule${R.importedRulesCount!==1?"s":""} from ${z.label}`;R.duplicatesCount>0&&(re+=`, ${R.duplicatesCount} duplicate${R.duplicatesCount!==1?"s":""} skipped`),R.totalAttempted>R.importedRulesCount+R.duplicatesCount&&(re+=`, ${R.totalAttempted-R.importedRulesCount-R.duplicatesCount} failed`),m(E,re),setTimeout(()=>{m(y,!1),m(E,"")},500)}catch(R){console.error("Failed to process auto-import selection:",R),m(A,"Failed to import rules. Please try again.")}finally{m(P,!1)}},cancel:function(){m(y,!1),m(A,""),m(E,"")}}}),l(r,ue),rt(),s()}var wd=bt("<svg><!></svg>"),bd=g('<div class="account-email svelte-wku0j5"><!> <!></div>'),$d=g("<!> <!>",1);function Sd(r,e){st(e,!1);const[t,s]=Et(),a=()=>et(c,"$userEmailStore",t),o=j();let i=w(e,"onSignOut",8),d=j(!1);const c=vs(fs.key).getUserEmail();function u(){i()(),m(d,!0)}ve(()=>a(),()=>{m(o,a())}),dt(),nt(),$a(r,{title:"",loading:!1,children:(p,oe)=>{var $=$d(),de=$e($),me=k=>{var I=bd(),q=h(I);ce(q,{size:1,color:"secondary",children:(T,y)=>{var L=N("Signed in as");l(T,L)},$$slots:{default:!0}});var G=v(q,2);ce(G,{size:1,weight:"medium",children:(T,y)=>{var L=N();fe(()=>ze(L,n(o))),l(T,L)},$$slots:{default:!0}}),l(k,I)};U(de,k=>{n(o)&&k(me)});var Z=v(de,2);Qe(Z,{get loading(){return n(d)},variant:"soft","data-testid":"sign-out-button",$$events:{click:u},children:(k,I)=>{var q=N("Sign Out");l(k,q)},$$slots:{default:!0,iconLeft:(k,I)=>{(function(q,G){const T=rs(G,["children","$$slots","$$events","$$legacy"]);var y=wd();ws(y,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...T}));var L=h(y);Hs(L,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',!0),l(q,y)})(k,{slot:"iconLeft"})}}}),l(p,$)},$$slots:{default:!0}}),rt(),s()}class kd{constructor(e,t,s){Ze(this,"_showCreateRuleDialog",ht(!1));Ze(this,"_createRuleError",ht(""));Ze(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=s;const a=new oa;this._extensionClient=new la(e,t,a)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:yr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Ws.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const s=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(s)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===qr?this._extensionClient.openFile({repoRoot:t,pathName:qr}):this._extensionClient.openFile({repoRoot:t,pathName:`${ln}/${dn}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:Je.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?Ws.selectedDirectory:(e.directoryOrFile,Ws.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:yr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:yr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Ws.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}var xd=g('<span slot="content"><!></span>');function Md(r,e){st(e,!1);const[t,s]=Et(),a=()=>et(V,"$guidelines",t),o=()=>et(P,"$settingsComponentSupported",t),i=()=>et(A,"$enableAgentMode",t),d=()=>et(O,"$terminalSettingsStore",t),c=j(),u=j(),p=j(),oe=j(),$=j(),de=new fs(ut),me=new Gs(ut),Z=new Di(ut),k=new Va(ut),I=new oa,q=new la(ut,k,I),G=new Sr(k),T=new qs(k),y=new cn(k),L=new kd(ut,k,y);k.registerConsumer(y),Js(Sr.key,G),Js(qs.key,T),Js(fs.key,de),vn(q),function(F){Js(Sa,F)}(me);const P=de.getSettingsComponentSupported(),A=de.getEnableAgentMode(),E=de.getEnableAgentSwarmMode(),K=de.getHasEverUsedRemoteAgent();k.registerConsumer(de),k.registerConsumer(me),k.registerConsumer(Z);const O=Z.getTerminalSettings();let _=j();const b={handleMessageFromExtension:F=>!(!F.data||F.data.type!==Je.navigateToSettingsSection)&&(F.data.data&&typeof F.data.data=="string"&&m(_,F.data.data),!0)};k.registerConsumer(b);const ue=de.getDisplayableTools(),V=de.getGuidelines();function x(F){const pe=F.trim();return!(n(u)&&pe.length>n(u))&&(de.updateLocalUserGuidelines(pe),ut.postMessage({type:Je.updateUserGuidelines,data:F}),!0)}function te(F){ut.postMessage({type:Je.toolConfigStartOAuth,data:{authUrl:F}}),de.startPolling()}async function H(F){await q.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${F.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&ut.postMessage({type:Je.toolConfigRevokeAccess,data:{toolId:F.identifier}})}function ae(F){Z.updateSelectedShell(F)}function he(F){Z.updateStartupScript(F)}function Te(F,pe){ut.postMessage({type:Je.toolApprovalConfigSetRequest,data:{toolId:F,approvalConfig:pe}})}function Me(){ut.postMessage({type:Je.signOut})}aa(()=>{de.dispose(),G.dispose(),T.dispose()}),de.notifyLoaded(),ut.postMessage({type:Je.getOrientationStatus}),ut.postMessage({type:Je.settingsPanelLoaded}),ve(()=>a(),()=>{var F;m(c,(F=a().userGuidelines)==null?void 0:F.contents)}),ve(()=>a(),()=>{var F;m(u,(F=a().userGuidelines)==null?void 0:F.lengthLimit)}),ve(()=>a(),()=>{var F,pe;m(p,(pe=(F=a().workspaceGuidelines)==null?void 0:F[0])==null?void 0:pe.lengthLimit)}),ve(()=>a(),()=>{var F,pe;m(oe,((pe=(F=a().workspaceGuidelines)==null?void 0:F[0])==null?void 0:pe.contents)||"")}),ve(()=>(o(),ra),()=>{m($,[o().remoteTools?xs("Tools","",$o,"section-tools"):void 0,o().userGuidelines&&!o().rules?xs("User Guidelines","Guidelines for Augment Chat to follow.",Jl,"guidelines"):void 0,o().rules?xs("Rules and User Guidelines","",Kl,"guidelines"):void 0,o().workspaceContext?xs("Context","",ko,"context"):void 0,xs("Account","Manage your Augment account settings.",ra,"account")].filter(Boolean))}),ve(()=>(n($),n(_)),()=>{var F;n($).length>1&&!n(_)&&m(_,(F=n($)[0])==null?void 0:F.id)}),dt(),nt(),Ct("message",Ir,function(...F){var pe;(pe=k.onMessageFromExtension)==null||pe.apply(this,F)}),Sn.Root(r,{children:(F,pe)=>{wo(F,{get items(){return n($)},mode:"tree",class:"c-settings-navigation",get selectedId(){return n(_)},$$slots:{content:(Ne,ye)=>{var je=xd();const se=xe(()=>ye.item);var Y=h(je),Oe=Re=>{},Ae=(Re,we)=>{var Se=M=>{ao(M,{})},qe=(M,z)=>{var R=Q=>{var X=lt(),W=$e(X),ne=le=>{Cd(le,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},get workspaceGuidelinesLengthLimit(){return n(p)},get workspaceGuidelinesContent(){return n(oe)},updateUserGuideline:x,get rulesModel(){return y},get rulesController(){return L}})},J=le=>{ka(le,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},updateUserGuideline:x})};U(W,le=>{o(),f(()=>o().rules)?le(ne):le(J,!1)}),l(Q,X)},re=(Q,X)=>{var W=J=>{Sd(J,{onSignOut:Me})},ne=J=>{const le=xe(()=>(i(),o(),f(()=>i()&&o().mcpServerList))),De=xe(()=>(i(),o(),f(()=>i()&&o().mcpServerImport)));Bl(J,{get tools(){return et(ue,"$displayableTools",t)},onAuthenticate:te,onRevokeAccess:H,onToolApprovalConfigChange:Te,onMCPServerAdd:ee=>me.addServer(ee),onMCPServerSave:ee=>me.updateServer(ee),onMCPServerDelete:ee=>me.deleteServer(ee),onMCPServerToggleDisable:ee=>me.toggleDisabledServer(ee),onMCPServerJSONImport:ee=>me.importServersFromJSON(ee),get isMCPEnabled(){return n(le)},get isMCPImportEnabled(){return n(De)},get supportedShells(){return d(),f(()=>d().supportedShells)},get selectedShell(){return d(),f(()=>d().selectedShell)},get startupScript(){return d(),f(()=>d().startupScript)},onShellSelect:ae,onStartupScriptChange:he,get isTerminalEnabled(){return o(),f(()=>o().terminal)},isSoundCategoryEnabled:!0,get isAgentCategoryEnabled(){return i()},get isSwarmModeFeatureFlagEnabled(){return et(E,"$enableAgentSwarmMode",t)},get hasEverUsedRemoteAgent(){return et(K,"$hasEverUsedRemoteAgent",t)}})};U(Q,J=>{C(n(se)),f(()=>{var le;return((le=n(se))==null?void 0:le.id)==="account"})?J(W):J(ne,!1)},X)};U(M,Q=>{C(n(se)),f(()=>{var X;return((X=n(se))==null?void 0:X.id)==="guidelines"})?Q(R):Q(re,!1)},z)};U(Re,M=>{C(n(se)),f(()=>{var z;return((z=n(se))==null?void 0:z.id)==="context"})?M(Se):M(qe,!1)},we)};U(Y,Re=>{C(Nr),C(n(se)),f(()=>!Nr(n(se)))?Re(Oe):Re(Ae,!1)}),l(Ne,je)}}})},$$slots:{default:!0}}),rt(),s()}(async function(){ut&&ut.initialize&&await ut.initialize(),Fa(Md,{target:document.getElementById("app")})})();
