<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agent Diff</title>
    <script nonce="nonce-//32PzpVWxP64kLwdMFbww==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <script type="module" crossorigin src="./assets/remote-agent-diff-wRwzUYC4.js" nonce="nonce-//32PzpVWxP64kLwdMFbww=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BY2Lraps.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-DkEuonq_.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-B8y0FMb_.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-BnOo7nYC.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-BauNv3yh.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-3WeVBTrk.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CAuiPHBF.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BaFOe6RO.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-CllkyJKm.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/index-ALhsmmIa.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-DfqKRK9G.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-7QsRDmJG.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/index-CoHT-xzg.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/index-BPm23rLE.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-TFiyZoZ6.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-Bvj-edDt.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-BoJU5mQc.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-CKuUXxrb.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-BvE8QmB9.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BF0Ta3I3.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-BvhJgadP.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BtrmW4jo.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-B56C2vkv.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-MyOXHVsl.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-diff-Bzk3Sgw8.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-panel-base-CVwMZATI.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
