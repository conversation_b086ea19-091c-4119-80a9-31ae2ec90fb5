<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-//32PzpVWxP64kLwdMFbww==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <script type="module" crossorigin src="./assets/remote-agent-home-CLu3m8eo.js" nonce="nonce-//32PzpVWxP64kLwdMFbww=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BY2Lraps.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-DkEuonq_.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-B8y0FMb_.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-BnOo7nYC.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-BauNv3yh.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-XI3B217g.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-CISyK7_6.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BaFOe6RO.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-BPYQDfw6.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BF0Ta3I3.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-DFXa-EF4.js" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-DCeibtmX.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BtrmW4jo.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-C-z-uXWx.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-C6PydsUO.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-panel-base-CVwMZATI.css" nonce="nonce-//32PzpVWxP64kLwdMFbww==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
