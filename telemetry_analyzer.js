/**
 * Augment Extension Telemetry Analyzer
 * 
 * This script analyzes the extension code to find the actual functions
 * that collect and send telemetry data, rather than blindly patching.
 */

const fs = require('fs');
const path = require('path');

const EXTENSION_PATH = path.join(process.env.HOME, '.vscode/extensions/augment.vscode-augment-0.525.0');
const EXTENSION_JS_PATH = path.join(EXTENSION_PATH, 'out/extension.js');

class TelemetryAnalyzer {
    constructor() {
        this.extensionCode = '';
        this.telemetryFunctions = [];
        this.networkCalls = [];
        this.dataCollectors = [];
    }

    async analyze() {
        console.log('🔍 Analyzing Augment extension for telemetry functions...');
        
        if (!fs.existsSync(EXTENSION_JS_PATH)) {
            console.error('❌ Extension not found at:', EXTENSION_JS_PATH);
            return;
        }

        this.extensionCode = fs.readFileSync(EXTENSION_JS_PATH, 'utf8');
        console.log(`📄 Loaded extension code: ${Math.round(this.extensionCode.length / 1024)}KB`);

        // Step 1: Find network/HTTP functions
        this.findNetworkFunctions();
        
        // Step 2: Find data collection patterns
        this.findDataCollectors();
        
        // Step 3: Find telemetry-specific patterns
        this.findTelemetryPatterns();
        
        // Step 4: Analyze function relationships
        this.analyzeCallChains();
        
        // Step 5: Generate report
        this.generateReport();
    }

    findNetworkFunctions() {
        console.log('🌐 Searching for network/HTTP functions...');
        
        const networkPatterns = [
            // HTTP/HTTPS requests
            /\.request\s*\([^)]*\)/g,
            /\.post\s*\([^)]*\)/g,
            /\.get\s*\([^)]*\)/g,
            /\.put\s*\([^)]*\)/g,
            
            // Fetch API
            /fetch\s*\([^)]*\)/g,
            
            // XMLHttpRequest
            /new\s+XMLHttpRequest\s*\(\)/g,
            /\.send\s*\([^)]*\)/g,
            
            // WebSocket
            /new\s+WebSocket\s*\([^)]*\)/g,
            
            // Axios or other HTTP libraries
            /axios\.[a-z]+\s*\([^)]*\)/g,
            
            // URL patterns that might be endpoints
            /https?:\/\/[a-zA-Z0-9.-]+[^"'\s)]+/g
        ];

        networkPatterns.forEach((pattern, index) => {
            const matches = [...this.extensionCode.matchAll(pattern)];
            if (matches.length > 0) {
                console.log(`  📡 Pattern ${index + 1}: Found ${matches.length} matches`);
                matches.forEach(match => {
                    this.networkCalls.push({
                        pattern: pattern.toString(),
                        match: match[0],
                        index: match.index,
                        context: this.getContext(match.index, 200)
                    });
                });
            }
        });
    }

    findDataCollectors() {
        console.log('📊 Searching for data collection patterns...');
        
        const dataPatterns = [
            // System info collection
            /os\.[a-zA-Z]+\s*\(\)/g,
            /process\.[a-zA-Z]+/g,
            /vscode\.env\.[a-zA-Z]+/g,
            /vscode\.version/g,
            /navigator\.[a-zA-Z]+/g,
            
            // User/workspace info
            /workspace\.[a-zA-Z]+/g,
            /window\.activeTextEditor/g,
            /document\.[a-zA-Z]+/g,
            
            // Git info
            /git[A-Z][a-zA-Z]*/g,
            /\.git\//g,
            
            // Performance/timing
            /performance\.[a-zA-Z]+/g,
            /Date\.now\(\)/g,
            /new\s+Date\(\)/g,
            
            // Error tracking
            /Error\s*\(/g,
            /catch\s*\(/g,
            /\.stack/g,
            
            // Analytics/tracking keywords
            /track[A-Z][a-zA-Z]*/g,
            /analytics?[A-Z][a-zA-Z]*/g,
            /telemetry[A-Z][a-zA-Z]*/g,
            /metrics?[A-Z][a-zA-Z]*/g,
            /usage[A-Z][a-zA-Z]*/g,
            /event[A-Z][a-zA-Z]*/g
        ];

        dataPatterns.forEach((pattern, index) => {
            const matches = [...this.extensionCode.matchAll(pattern)];
            if (matches.length > 0) {
                console.log(`  📈 Data pattern ${index + 1}: Found ${matches.length} matches`);
                matches.forEach(match => {
                    this.dataCollectors.push({
                        pattern: pattern.toString(),
                        match: match[0],
                        index: match.index,
                        context: this.getContext(match.index, 150)
                    });
                });
            }
        });
    }

    findTelemetryPatterns() {
        console.log('🎯 Searching for specific telemetry patterns...');
        
        // Look for function definitions that might be telemetry-related
        const functionPatterns = [
            /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*{[^}]*(?:track|analytics|telemetry|send|report|collect)[^}]*}/gi,
            /[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]\s*(?:function\s*)?\([^)]*\)\s*=>\s*{[^}]*(?:track|analytics|telemetry|send|report|collect)[^}]*}/gi,
            /class\s+[a-zA-Z_$][a-zA-Z0-9_$]*[^{]*{[^}]*(?:track|analytics|telemetry|send|report|collect)[^}]*}/gi
        ];

        functionPatterns.forEach((pattern, index) => {
            const matches = [...this.extensionCode.matchAll(pattern)];
            if (matches.length > 0) {
                console.log(`  🎯 Telemetry pattern ${index + 1}: Found ${matches.length} matches`);
                matches.forEach(match => {
                    this.telemetryFunctions.push({
                        pattern: pattern.toString(),
                        match: match[0],
                        index: match.index,
                        context: this.getContext(match.index, 300)
                    });
                });
            }
        });
    }

    analyzeCallChains() {
        console.log('🔗 Analyzing function call chains...');
        
        // Find functions that call network functions
        this.networkCalls.forEach(networkCall => {
            const functionContext = this.getFunctionContext(networkCall.index);
            if (functionContext) {
                console.log(`  📞 Network call in function: ${functionContext.name || 'anonymous'}`);
                networkCall.functionContext = functionContext;
            }
        });
    }

    getContext(index, length = 200) {
        const start = Math.max(0, index - length);
        const end = Math.min(this.extensionCode.length, index + length);
        return this.extensionCode.substring(start, end);
    }

    getFunctionContext(index) {
        // Find the function that contains this index
        const beforeCode = this.extensionCode.substring(0, index);
        const functionMatch = beforeCode.match(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\([^)]*\)\s*{[^}]*$/);
        
        if (functionMatch) {
            return {
                name: functionMatch[1],
                fullMatch: functionMatch[0]
            };
        }
        
        // Try arrow function pattern
        const arrowMatch = beforeCode.match(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[:=]\s*(?:function\s*)?\([^)]*\)\s*=>\s*{[^}]*$/);
        if (arrowMatch) {
            return {
                name: arrowMatch[1],
                fullMatch: arrowMatch[0]
            };
        }
        
        return null;
    }

    generateReport() {
        console.log('\n📋 TELEMETRY ANALYSIS REPORT');
        console.log('=' .repeat(50));
        
        console.log(`\n🌐 Network Functions Found: ${this.networkCalls.length}`);
        this.networkCalls.slice(0, 10).forEach((call, index) => {
            console.log(`\n${index + 1}. ${call.match}`);
            console.log(`   Context: ${call.context.replace(/\s+/g, ' ').substring(0, 100)}...`);
            if (call.functionContext) {
                console.log(`   Function: ${call.functionContext.name}`);
            }
        });

        console.log(`\n📊 Data Collectors Found: ${this.dataCollectors.length}`);
        const uniqueCollectors = [...new Set(this.dataCollectors.map(d => d.match))];
        uniqueCollectors.slice(0, 15).forEach((collector, index) => {
            console.log(`${index + 1}. ${collector}`);
        });

        console.log(`\n🎯 Telemetry Functions Found: ${this.telemetryFunctions.length}`);
        this.telemetryFunctions.forEach((func, index) => {
            console.log(`\n${index + 1}. Function snippet:`);
            console.log(`   ${func.match.substring(0, 200)}...`);
        });

        // Save detailed report
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                networkCalls: this.networkCalls.length,
                dataCollectors: this.dataCollectors.length,
                telemetryFunctions: this.telemetryFunctions.length
            },
            networkCalls: this.networkCalls,
            dataCollectors: this.dataCollectors,
            telemetryFunctions: this.telemetryFunctions
        };

        const reportPath = path.join(__dirname, 'telemetry_analysis_report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n💾 Detailed report saved to: ${reportPath}`);

        console.log('\n🎯 NEXT STEPS:');
        console.log('1. Review the detailed report to identify actual telemetry functions');
        console.log('2. Use the function names and patterns to create targeted patches');
        console.log('3. Test patches by monitoring network traffic');
        console.log('4. Verify that fake data is being sent instead of real data');
    }
}

// Run the analyzer
const analyzer = new TelemetryAnalyzer();
analyzer.analyze().catch(console.error);
