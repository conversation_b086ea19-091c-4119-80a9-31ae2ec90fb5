/**
 * Augment VS Code Extension Telemetry Faker
 * 
 * This script helps you modify the Augment extension to send fake telemetry data
 * instead of real system information. Based on the user's description of 41 data points
 * being collected and sent to Augment's servers.
 * 
 * USAGE:
 * 1. Backup your original extension
 * 2. Run this script to patch the extension
 * 3. Restart VS Code
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Path to the Augment extension
const EXTENSION_PATH = path.join(process.env.HOME, '.vscode/extensions/augment.vscode-augment-0.525.0');
const EXTENSION_JS_PATH = path.join(EXTENSION_PATH, 'out/extension.js');

// Generate fake but consistent system data
function generateFakeSystemData() {
    const fakeData = {
        // System identifiers
        machineId: crypto.randomUUID(),
        deviceId: crypto.randomUUID(),
        hardwareId: crypto.randomUUID(),
        serialNumber: `FAKE-${Math.random().toString(36).substring(2, 15).toUpperCase()}`,
        
        // Network info
        macAddress: Array.from({length: 6}, () => 
            Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
        ).join(':'),
        hostname: `fake-machine-${Math.random().toString(36).substring(2, 8)}`,
        
        // System info
        platform: 'linux', // Always report as Linux
        arch: 'x64',
        version: '5.4.0-fake-kernel',
        release: '20.04.1-Ubuntu',
        
        // Hardware info
        cpuModel: 'Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz',
        cpuCores: 8,
        totalMemory: 16 * 1024 * 1024 * 1024, // 16GB
        freeMemory: 8 * 1024 * 1024 * 1024,   // 8GB
        
        // User info
        username: `user${Math.floor(Math.random() * 1000)}`,
        homedir: '/home/<USER>',
        
        // Environment
        nodeVersion: '18.17.0',
        npmVersion: '9.6.7',
        
        // VS Code info
        vscodeVersion: '1.85.0',
        extensionVersion: '0.525.0',
        
        // Timing data
        uptime: Math.floor(Math.random() * 86400), // Random uptime in seconds
        timestamp: Date.now(),
        
        // Additional fake data points to reach 41 total
        diskSpace: 500 * 1024 * 1024 * 1024, // 500GB
        timezone: 'UTC',
        locale: 'en-US',
        shell: '/bin/bash',
        terminal: 'xterm-256color',
        display: ':0',
        sessionId: crypto.randomUUID(),
        processId: Math.floor(Math.random() * 65535),
        parentProcessId: Math.floor(Math.random() * 65535),
        workingDirectory: '/home/<USER>/projects',
        gitUser: '<EMAIL>',
        gitConfig: 'fake-git-config',
        
        // Network interfaces (fake)
        networkInterfaces: {
            'eth0': {
                address: '*************',
                netmask: '*************',
                family: 'IPv4',
                mac: 'aa:bb:cc:dd:ee:ff',
                internal: false
            }
        },
        
        // Additional system metrics
        loadAverage: [0.5, 0.7, 0.8],
        systemUptime: Math.floor(Math.random() * 604800), // Random week
        
        // Fake fingerprinting data
        screenResolution: '1920x1080',
        colorDepth: 24,
        pixelRatio: 1,
        
        // Browser/runtime info
        userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        
        // Development environment
        installedExtensions: ['ms-python.python', 'ms-vscode.vscode-typescript-next'],
        recentFiles: ['/home/<USER>/project/main.py', '/home/<USER>/project/config.json'],
        
        // Final data point to make exactly 41
        uniqueFingerprint: crypto.createHash('sha256').update(JSON.stringify({
            fake: true,
            timestamp: Date.now(),
            random: Math.random()
        })).digest('hex')
    };
    
    return fakeData;
}

// Create fake system function replacements
function createFakeSystemFunctions() {
    const fakeData = generateFakeSystemData();
    
    return `
// Fake system data - injected by telemetry faker
const FAKE_SYSTEM_DATA = ${JSON.stringify(fakeData, null, 2)};

// Override system information functions
const originalRequire = require;
require = function(moduleName) {
    if (moduleName === 'os') {
        return {
            platform: () => FAKE_SYSTEM_DATA.platform,
            arch: () => FAKE_SYSTEM_DATA.arch,
            release: () => FAKE_SYSTEM_DATA.release,
            version: () => FAKE_SYSTEM_DATA.version,
            hostname: () => FAKE_SYSTEM_DATA.hostname,
            homedir: () => FAKE_SYSTEM_DATA.homedir,
            userInfo: () => ({ username: FAKE_SYSTEM_DATA.username }),
            totalmem: () => FAKE_SYSTEM_DATA.totalMemory,
            freemem: () => FAKE_SYSTEM_DATA.freeMemory,
            uptime: () => FAKE_SYSTEM_DATA.systemUptime,
            loadavg: () => FAKE_SYSTEM_DATA.loadAverage,
            networkInterfaces: () => FAKE_SYSTEM_DATA.networkInterfaces,
            cpus: () => Array(FAKE_SYSTEM_DATA.cpuCores).fill({
                model: FAKE_SYSTEM_DATA.cpuModel,
                speed: 3700,
                times: { user: 1000, nice: 0, sys: 500, idle: 10000, irq: 0 }
            }),
            EOL: '\\n'
        };
    }
    
    if (moduleName === 'crypto') {
        const originalCrypto = originalRequire('crypto');
        return {
            ...originalCrypto,
            randomUUID: () => FAKE_SYSTEM_DATA.machineId,
            createHash: originalCrypto.createHash,
            randomBytes: originalCrypto.randomBytes
        };
    }
    
    return originalRequire(moduleName);
};

// Override process object
if (typeof process !== 'undefined') {
    Object.defineProperty(process, 'platform', { value: FAKE_SYSTEM_DATA.platform });
    Object.defineProperty(process, 'arch', { value: FAKE_SYSTEM_DATA.arch });
    Object.defineProperty(process, 'version', { value: 'v' + FAKE_SYSTEM_DATA.nodeVersion });
    Object.defineProperty(process, 'versions', { 
        value: { 
            node: FAKE_SYSTEM_DATA.nodeVersion,
            npm: FAKE_SYSTEM_DATA.npmVersion,
            v8: '10.2.154.26-node.26'
        } 
    });
    Object.defineProperty(process, 'pid', { value: FAKE_SYSTEM_DATA.processId });
    Object.defineProperty(process, 'ppid', { value: FAKE_SYSTEM_DATA.parentProcessId });
    Object.defineProperty(process, 'cwd', { value: () => FAKE_SYSTEM_DATA.workingDirectory });
    Object.defineProperty(process, 'uptime', { value: () => FAKE_SYSTEM_DATA.uptime });
    
    // Override process.env with fake environment variables
    const originalEnv = process.env;
    process.env = new Proxy(originalEnv, {
        get(target, prop) {
            // Return fake values for common environment variables
            const fakeEnvVars = {
                'USER': FAKE_SYSTEM_DATA.username,
                'USERNAME': FAKE_SYSTEM_DATA.username,
                'HOME': FAKE_SYSTEM_DATA.homedir,
                'HOMEPATH': FAKE_SYSTEM_DATA.homedir,
                'HOSTNAME': FAKE_SYSTEM_DATA.hostname,
                'COMPUTERNAME': FAKE_SYSTEM_DATA.hostname,
                'SHELL': FAKE_SYSTEM_DATA.shell,
                'TERM': FAKE_SYSTEM_DATA.terminal,
                'DISPLAY': FAKE_SYSTEM_DATA.display,
                'TZ': FAKE_SYSTEM_DATA.timezone,
                'LANG': FAKE_SYSTEM_DATA.locale,
                'PWD': FAKE_SYSTEM_DATA.workingDirectory
            };
            
            if (fakeEnvVars.hasOwnProperty(prop)) {
                return fakeEnvVars[prop];
            }
            
            return target[prop];
        }
    });
}

// Console log to verify injection
console.log('[TELEMETRY FAKER] Fake system data injected successfully');
`;
}

function backupExtension() {
    const backupPath = EXTENSION_JS_PATH + '.backup';
    if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(EXTENSION_JS_PATH, backupPath);
        console.log('✅ Extension backed up to:', backupPath);
    } else {
        console.log('ℹ️  Backup already exists');
    }
}

function patchExtension() {
    try {
        // Read the original extension file
        let extensionCode = fs.readFileSync(EXTENSION_JS_PATH, 'utf8');
        
        // Check if already patched
        if (extensionCode.includes('FAKE_SYSTEM_DATA')) {
            console.log('⚠️  Extension already appears to be patched');
            return;
        }
        
        // Inject fake system functions at the beginning
        const fakeSystemCode = createFakeSystemFunctions();
        const patchedCode = fakeSystemCode + '\n\n' + extensionCode;
        
        // Write the patched extension
        fs.writeFileSync(EXTENSION_JS_PATH, patchedCode);
        
        console.log('✅ Extension patched successfully!');
        console.log('🔄 Please restart VS Code for changes to take effect');
        
    } catch (error) {
        console.error('❌ Error patching extension:', error.message);
    }
}

function restoreExtension() {
    const backupPath = EXTENSION_JS_PATH + '.backup';
    if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, EXTENSION_JS_PATH);
        console.log('✅ Extension restored from backup');
    } else {
        console.log('❌ No backup found');
    }
}

function main() {
    const command = process.argv[2];
    
    if (!fs.existsSync(EXTENSION_JS_PATH)) {
        console.error('❌ Augment extension not found at:', EXTENSION_JS_PATH);
        console.log('Please make sure the extension is installed and the path is correct');
        return;
    }
    
    switch (command) {
        case 'patch':
            backupExtension();
            patchExtension();
            break;
        case 'restore':
            restoreExtension();
            break;
        case 'backup':
            backupExtension();
            break;
        default:
            console.log('Usage: node augment_telemetry_faker.js [patch|restore|backup]');
            console.log('  patch   - Apply fake telemetry patch');
            console.log('  restore - Restore original extension');
            console.log('  backup  - Create backup only');
    }
}

if (require.main === module) {
    main();
}

module.exports = {
    generateFakeSystemData,
    createFakeSystemFunctions,
    patchExtension,
    restoreExtension
};
